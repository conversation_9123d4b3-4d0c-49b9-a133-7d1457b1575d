{"title": "MCP-Server", "done": "<PERSON><PERSON><PERSON>", "marketplace": "MCP-Marktplatz", "description": "Das <0>Model Context Protocol</0> ermöglicht die Kommunikation mit lokal laufenden MCP-Servern, die zusätzliche Tools und Ressourcen zur Erweiterung der Fähigkeiten von Roo bereitstellen. Du kannst <1>von der Community erstellte Server</1> verwenden oder Roo bitten, neue Tools speziell für deinen Workflow zu erstellen (z.B. \"ein Tool hinzufügen, das die neueste npm-Dokumentation abruft\").", "instructions": "Anweisungen", "enableToggle": {"title": "MCP-Server aktivieren", "description": "<PERSON><PERSON><PERSON> dies E<PERSON>, damit <PERSON><PERSON> von verbundenen MCP-<PERSON>n verwenden kann. Dies gibt Roo mehr Möglichkeiten. Wenn du diese zusätzlichen Tools nicht verwenden möchtest, schalte es AUS, um API-Token-Kosten zu senken."}, "enableServerCreation": {"title": "MCP-Server-Erstellung aktivieren", "description": "<PERSON>kt<PERSON><PERSON><PERSON> dies, damit <PERSON> dir helfen kann, <1>neue</1> benutzerdefinierte MCP-Server zu erstellen. <0>Erfahre mehr über Server-Erstellung</0>", "hint": "Hinweis: Um API-Token-<PERSON><PERSON> zu senken, deaktiviere diese Einstellung, wenn du Roo nicht aktiv darum bittest, einen neuen MCP-Server zu erstellen."}, "editGlobalMCP": "Globale MCP bearbeiten", "editProjectMCP": "Projekt-MCP bearbeiten", "learnMoreEditingSettings": "Mehr über das Bearbeiten von MCP-Einstellungsdateien erfahren", "tool": {"alwaysAllow": "<PERSON><PERSON> erlauben", "parameters": "Parameter", "noDescription": "<PERSON><PERSON>", "togglePromptInclusion": "Einbeziehung in Prompt umschalten"}, "tabs": {"tools": "Tools", "resources": "Ressourcen", "errors": "<PERSON><PERSON>"}, "emptyState": {"noTools": "<PERSON><PERSON> gefunden", "noResources": "<PERSON><PERSON>n gefunden", "noErrors": "<PERSON><PERSON> gefunden"}, "networkTimeout": {"label": "Netzwerk-Timeout", "description": "Maximale Wartezeit für Serverantworten", "options": {"15seconds": "15 Sekunden", "30seconds": "30 Sekunden", "1minute": "1 Minute", "5minutes": "5 Minuten", "10minutes": "10 Minuten", "15minutes": "15 Minuten", "30minutes": "30 Minuten", "60minutes": "60 Minuten"}}, "deleteDialog": {"title": "MCP-Server löschen", "description": "<PERSON><PERSON> du sic<PERSON>, dass du den MCP-Server \"{{serverName}}\" löschen möchtest? Diese Aktion kann nicht rückgängig gemacht werden.", "cancel": "Abbrechen", "delete": "Löschen"}, "serverStatus": {"retrying": "<PERSON><PERSON><PERSON><PERSON>...", "retryConnection": "<PERSON>er<PERSON><PERSON><PERSON> wieder<PERSON>n"}, "refreshMCP": "MCP-Server aktualisieren", "execution": {"running": "Wird ausgeführt", "completed": "Abgeschlossen", "error": "<PERSON><PERSON>"}}
{"greeting": "Welcome to Roo Code!", "task": {"title": "Task", "seeMore": "See more", "seeLess": "See less", "tokens": "Tokens:", "cache": "Cache:", "apiCost": "API Cost:", "condenseContext": "Intelligently condense context", "contextWindow": "Context Length:", "closeAndStart": "Close task and start a new one", "export": "Export task history", "share": "Share task", "delete": "Delete Task (<PERSON><PERSON> + <PERSON>lick to skip confirmation)", "shareWithOrganization": "Share with Organization", "shareWithOrganizationDescription": "Only members of your organization can access", "sharePublicly": "Share Publicly", "sharePubliclyDescription": "Anyone with the link can access", "connectToCloud": "Connect to Cloud", "connectToCloudDescription": "Sign in to Roo Code Cloud to share tasks", "sharingDisabledByOrganization": "Sharing disabled by organization", "shareSuccessOrganization": "Organization link copied to clipboard", "shareSuccessPublic": "Public link copied to clipboard"}, "unpin": "Unpin", "pin": "<PERSON>n", "retry": {"title": "Retry", "tooltip": "Try the operation again"}, "startNewTask": {"title": "Start New Task", "tooltip": "Begin a new task"}, "proceedAnyways": {"title": "Proceed Anyways", "tooltip": "Continue while command executes"}, "save": {"title": "Save", "tooltip": "Save the file changes"}, "tokenProgress": {"availableSpace": "Available space: {{amount}} tokens", "tokensUsed": "Tokens used: {{used}} of {{total}}", "reservedForResponse": "Reserved for model response: {{amount}} tokens"}, "reject": {"title": "Reject", "tooltip": "Reject this action"}, "completeSubtaskAndReturn": "Complete Subtask and Return", "approve": {"title": "Approve", "tooltip": "Approve this action"}, "read-batch": {"approve": {"title": "Approve All"}, "deny": {"title": "<PERSON><PERSON>"}}, "runCommand": {"title": "Run Command", "tooltip": "Execute this command"}, "proceedWhileRunning": {"title": "Proceed While Running", "tooltip": "Continue despite warnings"}, "killCommand": {"title": "Kill Command", "tooltip": "Kill the current command"}, "resumeTask": {"title": "Resume Task", "tooltip": "Continue the current task"}, "terminate": {"title": "Terminate", "tooltip": "End the current task"}, "cancel": {"title": "Cancel", "tooltip": "Cancel the current operation"}, "scrollToBottom": "Scroll to bottom of chat", "about": "Generate, refactor, and debug code with AI assistance. Check out our <DocsLink>documentation</DocsLink> to learn more.", "onboarding": "Your task list in this workspace is empty.", "rooTips": {"boomerangTasks": {"title": "Task Orchestration", "description": "Split tasks into smaller, manageable parts"}, "stickyModels": {"title": "Sticky Models", "description": "Each mode remembers your last used model"}, "tools": {"title": "Tools", "description": "Allow the AI to solve problems by browsing the web, running commands, and more"}, "customizableModes": {"title": "Customizable Modes", "description": "Specialized personas with their own behaviors and assigned models"}}, "selectMode": "Select mode for interaction", "selectApiConfig": "Select API configuration", "enhancePrompt": "Enhance prompt with additional context", "modeSelector": {"title": "Modes", "marketplace": "Mode Marketplace", "settings": "Mode Settings", "description": "Specialized personas that tailor <PERSON><PERSON>'s behavior."}, "enhancePromptDescription": "The 'Enhance Prompt' button helps improve your prompt by providing additional context, clarification, or rephrasing. Try typing a prompt in here and clicking the button again to see how it works.", "addImages": "Add images to message", "sendMessage": "Send message", "stopTts": "Stop text-to-speech", "typeMessage": "Type a message...", "typeTask": "Type your task here...", "addContext": "@ to add context, / to switch modes", "dragFiles": "hold shift to drag in files", "dragFilesImages": "hold shift to drag in files/images", "errorReadingFile": "Error reading file:", "noValidImages": "No valid images were processed", "separator": "Separator", "edit": "Edit...", "forNextMode": "for next mode", "apiRequest": {"title": "API Request", "failed": "API Request Failed", "streaming": "API Request...", "cancelled": "API Request Cancelled", "streamingFailed": "API Streaming Failed"}, "checkpoint": {"initial": "Initial Checkpoint", "regular": "Checkpoint", "initializingWarning": "Still initializing checkpoint... If this takes too long, you can disable checkpoints in <settingsLink>settings</settingsLink> and restart your task.", "menu": {"viewDiff": "View Diff", "restore": "Restore Checkpoint", "restoreFiles": "Restore Files", "restoreFilesDescription": "Restores your project's files back to a snapshot taken at this point.", "restoreFilesAndTask": "Restore Files & Task", "confirm": "Confirm", "cancel": "Cancel", "cannotUndo": "This action cannot be undone.", "restoreFilesAndTaskDescription": "Restores your project's files back to a snapshot taken at this point and deletes all messages after this point."}, "current": "Current"}, "contextCondense": {"title": "Context Condensed", "condensing": "Condensing context...", "errorHeader": "Failed to condense context", "tokens": "tokens"}, "instructions": {"wantsToFetch": "<PERSON><PERSON> wants to fetch detailed instructions to assist with the current task"}, "fileOperations": {"wantsToRead": "<PERSON><PERSON> wants to read this file:", "wantsToReadMultiple": "<PERSON><PERSON> wants to read multiple files:", "wantsToReadAndXMore": "<PERSON><PERSON> wants to read this file and {{count}} more:", "wantsToReadOutsideWorkspace": "<PERSON><PERSON> wants to read this file outside of the workspace:", "didRead": "<PERSON><PERSON> read this file:", "wantsToEdit": "<PERSON><PERSON> wants to edit this file:", "wantsToEditOutsideWorkspace": "<PERSON><PERSON> wants to edit this file outside of the workspace:", "wantsToEditProtected": "<PERSON><PERSON> wants to edit a protected configuration file:", "wantsToApplyBatchChanges": "<PERSON><PERSON> wants to apply changes to multiple files:", "wantsToCreate": "<PERSON><PERSON> wants to create a new file:", "wantsToSearchReplace": "<PERSON><PERSON> wants to search and replace in this file:", "didSearchReplace": "<PERSON><PERSON> performed search and replace on this file:", "wantsToInsert": "<PERSON><PERSON> wants to insert content into this file:", "wantsToInsertWithLineNumber": "<PERSON><PERSON> wants to insert content into this file at line {{lineNumber}}:", "wantsToInsertAtEnd": "<PERSON><PERSON> wants to append content to the end of this file:"}, "directoryOperations": {"wantsToViewTopLevel": "<PERSON><PERSON> wants to view the top level files in this directory:", "didViewTopLevel": "<PERSON><PERSON> viewed the top level files in this directory:", "wantsToViewTopLevelOutsideWorkspace": "<PERSON><PERSON> wants to view the top level files in this directory (outside workspace):", "didViewTopLevelOutsideWorkspace": "<PERSON><PERSON> viewed the top level files in this directory (outside workspace):", "wantsToViewRecursive": "<PERSON><PERSON> wants to recursively view all files in this directory:", "didViewRecursive": "<PERSON><PERSON> recursively viewed all files in this directory:", "wantsToViewRecursiveOutsideWorkspace": "<PERSON><PERSON> wants to recursively view all files in this directory (outside workspace):", "didViewRecursiveOutsideWorkspace": "<PERSON><PERSON> recursively viewed all files in this directory (outside workspace):", "wantsToViewDefinitions": "<PERSON><PERSON> wants to view source code definition names used in this directory:", "didViewDefinitions": "Roo viewed source code definition names used in this directory:", "wantsToViewDefinitionsOutsideWorkspace": "<PERSON><PERSON> wants to view source code definition names used in this directory (outside workspace):", "didViewDefinitionsOutsideWorkspace": "Roo viewed source code definition names used in this directory (outside workspace):", "wantsToSearch": "<PERSON><PERSON> wants to search this directory for <code>{{regex}}</code>:", "didSearch": "<PERSON><PERSON> searched this directory for <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "<PERSON><PERSON> wants to search this directory (outside workspace) for <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "<PERSON><PERSON> searched this directory (outside workspace) for <code>{{regex}}</code>:"}, "codebaseSearch": {"wantsToSearch": "<PERSON><PERSON> wants to search the codebase for <code>{{query}}</code>:", "wantsToSearchWithPath": "<PERSON><PERSON> wants to search the codebase for <code>{{query}}</code> in <code>{{path}}</code>:", "didSearch_one": "Found 1 result", "didSearch_other": "Found {{count}} results", "resultTooltip": "Similarity score: {{score}} (click to open file)"}, "commandOutput": "Command Output", "response": "Response", "arguments": "Arguments", "mcp": {"wantsToUseTool": "<PERSON><PERSON> wants to use a tool on the {{serverName}} MCP server:", "wantsToAccessResource": "<PERSON><PERSON> wants to access a resource on the {{serverName}} MCP server:"}, "modes": {"wantsToSwitch": "<PERSON><PERSON> wants to switch to {{mode}} mode", "wantsToSwitchWithReason": "<PERSON><PERSON> wants to switch to {{mode}} mode because: {{reason}}", "didSwitch": "<PERSON><PERSON> switched to {{mode}} mode", "didSwitchWithReason": "<PERSON><PERSON> switched to {{mode}} mode because: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON> wants to create a new subtask in {{mode}} mode:", "wantsToFinish": "<PERSON><PERSON> wants to finish this subtask", "newTaskContent": "Subtask Instructions", "completionContent": "Subtask Completed", "resultContent": "Subtask Results", "defaultResult": "Please continue to the next task.", "completionInstructions": "Subtask completed! You can review the results and suggest any corrections or next steps. If everything looks good, confirm to return the result to the parent task."}, "questions": {"hasQuestion": "<PERSON><PERSON> has a question:"}, "taskCompleted": "Task Completed", "error": "Error", "diffError": {"title": "Edit Unsuccessful"}, "troubleMessage": "<PERSON><PERSON> is having trouble...", "powershell": {"issues": "It seems like you're having Windows PowerShell issues, please see this"}, "autoApprove": {"title": "Auto-approve:", "none": "None", "description": "Auto-approve allows Roo Code to perform actions without asking for permission. Only enable for actions you fully trust. More detailed configuration available in <settingsLink>Settings</settingsLink>."}, "announcement": {"title": "🎉 Roo Code {{version}} Released", "description": "Roo Code {{version}} brings powerful new features and significant improvements to enhance your development workflow.", "whatsNew": "What's New", "feature1": "<bold>Codebase Indexing Graduated from Experimental</bold>: Full codebase indexing is now stable and ready for production use with improved search and context understanding.", "feature2": "<bold>New Todo List Feature</bold>: Keep your tasks on track with integrated todo management that helps you stay organized and focused on your development goals.", "hideButton": "Hide announcement", "detailsDiscussLinks": "Get more details and discuss in <discordLink>Discord</discordLink> and <redditLink>Reddit</redditLink> 🚀"}, "reasoning": {"thinking": "Thinking", "seconds": "{{count}}s"}, "followUpSuggest": {"copyToInput": "Copy to input (same as shift + click)", "autoSelectCountdown": "Auto-selecting in {{count}}s", "countdownDisplay": "{{count}}s"}, "browser": {"rooWantsToUse": "<PERSON><PERSON> wants to use the browser:", "consoleLogs": "Console <PERSON>gs", "noNewLogs": "(No new logs)", "screenshot": "Browser screenshot", "cursor": "cursor", "navigation": {"step": "Step {{current}} of {{total}}", "previous": "Previous", "next": "Next"}, "sessionStarted": "Browser Session Started", "actions": {"title": "Browse Action: ", "launch": "Launch browser at {{url}}", "click": "Click ({{coordinate}})", "type": "Type \"{{text}}\"", "scrollDown": "Scroll down", "scrollUp": "Scroll up", "close": "Close browser"}}, "codeblock": {"tooltips": {"expand": "Expand code block", "collapse": "Collapse code block", "enable_wrap": "Enable word wrap", "disable_wrap": "Disable word wrap", "copy_code": "Copy code"}}, "systemPromptWarning": "WARNING: Custom system prompt override active. This can severely break functionality and cause unpredictable behavior.", "profileViolationWarning": "The current profile isn't compatible with your organization's settings", "shellIntegration": {"title": "Command Execution Warning", "description": "Your command is being executed without VSCode terminal shell integration. To suppress this warning you can disable shell integration in the <strong>Terminal</strong> section of the <settingsLink>Roo Code settings</settingsLink> or troubleshoot VSCode terminal integration using the link below.", "troubleshooting": "Click here for shell integration documentation."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Auto-Approved Request Limit Reached", "description": "<PERSON><PERSON> has reached the auto-approved limit of {{count}} API request(s). Would you like to reset the count and proceed with the task?", "button": "Reset and Continue"}}, "indexingStatus": {"ready": "Index ready", "indexing": "Indexing {{percentage}}%", "indexed": "Indexed", "error": "Index error", "status": "Index status"}, "versionIndicator": {"ariaLabel": "Version {{version}} - Click to view release notes"}}
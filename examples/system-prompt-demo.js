#!/usr/bin/env node

/**
 * System Prompt Demo
 * 
 * This script demonstrates how the system prompt is automatically saved
 * alongside the api_conversation_history.json file for each task.
 * 
 * The system prompt contains all the context and instructions that the AI
 * uses to understand its role and capabilities for a specific task.
 */

const fs = require('fs');
const path = require('path');

// Example of what the system prompt data structure looks like
const exampleSystemPromptData = {
    prompt: `# Role
You are Augment Agent developed by Augment Code, an agentic coding AI assistant with access to the developer's codebase through Augment's world-leading context engine and integrations.
You can read from and write to the codebase using the provided tools.
The current date is 2025-06-18.

# Identity
Here is some information about Augment Agent in case the person asks:
The base model is Claude Sonnet 4 by Anthropic.
You are Augment Agent developed by Augment Code, an agentic coding AI assistant based on the Claude Sonnet 4 model by Anthropic, with access to the developer's codebase through Augment's world-leading context engine and integrations.

# Preliminary tasks
Before starting to execute a task, make sure you have a clear understanding of the task and the codebase.
Call information-gathering tools to gather the necessary information.
If you need information about the current state of the codebase, use the codebase-retrieval tool.

# Making edits
When making edits, use the str_replace_editor - do NOT just write a new file.
Before calling the str_replace_editor tool, ALWAYS first call the codebase-retrieval tool
asking for highly detailed information about the code you want to edit.

# Package Management
Always use appropriate package managers for dependency management instead of manually editing package configuration files.

# Following instructions
Focus on doing what the user asks you to do.
Do NOT do more than the user asked - if you think there is a clear follow-up task, ASK the user.

# Testing
You are very good at writing unit tests and making them work. If you write
code, suggest to the user to test the code by writing tests and running them.

# Final
If you have made code edits, always suggest writing or updating tests and executing those tests to make sure the changes are correct.`,
    timestamp: Date.now(),
    taskId: 'example-task-12345',
};

function demonstrateSystemPromptSaving() {
    console.log('🤖 System Prompt Saving Demo');
    console.log('============================\n');
    
    console.log('📋 What this feature does:');
    console.log('• Automatically saves the system prompt for each task');
    console.log('• Stores it in the same directory as api_conversation_history.json');
    console.log('• Includes timestamp and task ID for tracking');
    console.log('• Helps with debugging and understanding AI behavior\n');
    
    console.log('📁 File structure:');
    console.log('tasks/');
    console.log('└── {taskId}/');
    console.log('    ├── api_conversation_history.json  ← API messages');
    console.log('    ├── system_prompt.json             ← System prompt (NEW!)');
    console.log('    ├── ui_messages.json               ← UI messages');
    console.log('    └── task_metadata.json             ← Task metadata\n');
    
    console.log('📄 System prompt file format:');
    console.log(JSON.stringify({
        prompt: '[Full system prompt text...]',
        timestamp: 1750256719968,
        taskId: 'task-uuid-here'
    }, null, 2));
    console.log();
    
    console.log('🔧 Implementation details:');
    console.log('• Added to GlobalFileNames.systemPrompt');
    console.log('• New saveSystemPrompt() function in task-persistence');
    console.log('• Automatically called in Task.getSystemPrompt()');
    console.log('• No user intervention required - works automatically\n');
    
    console.log('💡 Use cases:');
    console.log('• Debug why AI behaved a certain way');
    console.log('• Understand what context was available');
    console.log('• Analyze prompt effectiveness');
    console.log('• Reproduce issues with exact same prompt\n');
    
    console.log('✅ Feature is now active and will save system prompts automatically!');
}

if (require.main === module) {
    demonstrateSystemPromptSaving();
}

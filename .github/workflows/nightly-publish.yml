name: Nightly Publish

on:
  push:
    branches: [main]
  workflow_dispatch: # Allows manual triggering.

jobs:
  publish-nightly:
    runs-on: ubuntu-latest

    permissions:
      contents: read # No tags pushed → read is enough.

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Setup Node.js and pnpm
        uses: ./.github/actions/setup-node-pnpm
        with:
          skip-checkout: 'true'
          install-args: '--frozen-lockfile'
      - name: Forge numeric Nightly version
        id: version
        env:
          RUN_NUMBER: ${{ github.run_number }}
        run: echo "number=$(( 5500 + ${RUN_NUMBER} ))" >> $GITHUB_OUTPUT
      - name: Patch package.json version
        env:
          VERSION_NUMBER: ${{ steps.version.outputs.number }}
        run: |
          node <<'EOF'
            const fs = require('fs');
            const path = require('path');
            const pkgPath = path.join(__dirname, 'apps', 'vscode-nightly', 'package.nightly.json');
            const pkg = JSON.parse(fs.readFileSync(pkgPath,'utf8'));
            const [maj, min] = pkg.version.split('.');
            pkg.version = `${maj}.${min}.${process.env.VERSION_NUMBER}`;
            fs.writeFileSync(pkgPath, JSON.stringify(pkg, null, 2));
            console.log(`🔖 Nightly version set to ${pkg.version}`);
          EOF
      - name: Build VSIX
        run: pnpm vsix:nightly # Produces bin/roo-code-nightly-0.0.[count].vsix
      - name: Publish to VS Code Marketplace
        env:
          VSCE_PAT: ${{ secrets.VSCE_PAT }}
        run: npx vsce publish --packagePath "bin/$(/bin/ls bin | head -n1)"
      - name: Publish to Open VSX Registry
        env:
          OVSX_PAT: ${{ secrets.OVSX_PAT }}
        run: npx ovsx publish "bin/$(ls bin | head -n1)"

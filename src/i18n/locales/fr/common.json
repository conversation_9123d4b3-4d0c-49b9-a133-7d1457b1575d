{"extension": {"name": "Roo Code", "description": "Une équipe complète de développeurs IA dans votre éditeur."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "welcome": "Bienvenue, {{name}} ! Vous avez {{count}} notification(s).", "items": {"zero": "Aucun élément", "one": "Un élément", "other": "{{count}} éléments"}, "confirmation": {"reset_state": "Êtes-vous sûr de vouloir réinitialiser le global state et le stockage de secrets de l'extension ? Cette action est irréversible.", "delete_config_profile": "Êtes-vous sûr de vouloir supprimer ce profil de configuration ?", "delete_custom_mode_with_rules": "Êtes-vous sûr de vouloir supprimer ce mode {scope} ?\n\nCela supprimera également le dossier de règles associé à l'adresse :\n{rulesFolderPath}", "delete_message": "Que souhaitez-vous supprimer ?", "edit_warning": "Modifier ce message supprimera tous les messages suivants dans la conversation. Voulez-vous continuer ?", "delete_just_this_message": "Uniquement ce message", "delete_this_and_subsequent": "Ce message et tous les messages suivants", "proceed": "<PERSON><PERSON><PERSON>"}, "errors": {"invalid_data_uri": "Format d'URI de données invalide", "error_copying_image": "Erreur lors de la copie de l'image : {{errorMessage}}", "error_saving_image": "Erreur lors de l'enregistrement de l'image : {{errorMessage}}", "error_opening_image": "Erreur lors de l'ouverture de l'image : {{error}}", "could_not_open_file": "Impossible d'ouvrir le fichier : {{errorMessage}}", "could_not_open_file_generic": "Impossible d'ouvrir le fichier !", "checkpoint_timeout": "Expiration du délai lors de la tentative de rétablissement du checkpoint.", "checkpoint_failed": "Échec du rétablissement du checkpoint.", "no_workspace": "Veuillez d'abord ouvrir un espace de travail", "update_support_prompt": "Erreur lors de la mise à jour du prompt de support", "reset_support_prompt": "Erreur lors de la réinitialisation du prompt de support", "enhance_prompt": "Erreur lors de l'amélioration du prompt", "get_system_prompt": "Erreur lors de l'obtention du prompt système", "search_commits": "<PERSON><PERSON>ur lors de la recherche des commits", "save_api_config": "Erreur lors de l'enregistrement de la configuration API", "create_api_config": "Erreur lors de la création de la configuration API", "rename_api_config": "Erreur lors du renommage de la configuration API", "load_api_config": "Erreur lors du chargement de la configuration API", "delete_api_config": "Erreur lors de la suppression de la configuration API", "list_api_config": "Erreur lors de l'obtention de la liste des configurations API", "update_server_timeout": "Erreur lors de la mise à jour du délai d'attente du serveur", "hmr_not_running": "Le serveur de développement local n'est pas en cours d'exécution, HMR ne fonctionnera pas. Veuillez exécuter 'npm run dev' avant de lancer l'extension pour activer l'HMR.", "retrieve_current_mode": "Erreur lors de la récupération du mode actuel à partir du state.", "failed_delete_repo": "Échec de la suppression du repo fantôme ou de la branche associée : {{error}}", "failed_remove_directory": "Échec de la suppression du répertoire de tâches : {{error}}", "custom_storage_path_unusable": "Le chemin de stockage personnalisé \"{{path}}\" est inutilisable, le chemin par défaut sera utilisé", "cannot_access_path": "Impossible d'accéder au chemin {{path}} : {{error}}", "settings_import_failed": "Échec de l'importation des paramètres : {{error}}", "mistake_limit_guidance": "<PERSON><PERSON> peut indiquer un échec dans le processus de réflexion du modèle ou une incapacité à utiliser un outil correctement, ce qui peut être atténué avec des conseils de l'utilisateur (par ex. \"Essaie de diviser la tâche en étapes plus petites\").", "violated_organization_allowlist": "Échec de l'exécution de la tâche : le profil actuel n'est pas compatible avec les paramètres de votre organisation", "condense_failed": "Échec de la condensation du contexte", "condense_not_enough_messages": "Pas assez de messages pour condenser le contexte", "condensed_recently": "Le contexte a été condensé récemment ; cette tentative est ignorée", "condense_handler_invalid": "Le gestionnaire d'API pour condenser le contexte est invalide", "condense_context_grew": "La taille du contexte a augmenté pendant la condensation ; cette tentative est ignorée", "url_timeout": "Le site web a pris trop de temps à charger (timeout). <PERSON>la pourrait être dû à une connexion lente, un site web lourd ou temporairement indisponible. Tu peux réessayer plus tard ou vérifier si l'URL est correcte.", "url_not_found": "L'adresse du site web n'a pas pu être trouvée. Vérifie si l'URL est correcte et réessaie.", "no_internet": "Pas de connexion internet. Vérifie ta connexion réseau et réessaie.", "url_forbidden": "L'accès à ce site web est interdit. Le site peut bloquer l'accès automatisé ou nécessiter une authentification.", "url_page_not_found": "La page n'a pas été trouvée. Vérifie si l'URL est correcte.", "url_fetch_failed": "Échec de récupération du contenu de l'URL : {{error}}", "url_fetch_error_with_url": "Erreur lors de la récupération du contenu pour {{url}} : {{error}}", "share_task_failed": "Échec du partage de la tâche. Veuillez réessayer.", "share_no_active_task": "Aucune tâche active à partager", "share_auth_required": "Authentification requise. Veuillez vous connecter pour partager des tâches.", "share_not_enabled": "Le partage de tâches n'est pas activé pour cette organisation.", "share_task_not_found": "Tâche non trouvée ou accès refusé.", "mode_import_failed": "Échec de l'importation du mode : {{error}}", "delete_rules_folder_failed": "Échec de la suppression du dossier de règles : {{rulesFolderPath}}. Erreur : {{error}}", "claudeCode": {"processExited": "Le processus Claude Code s'est terminé avec le code {{exitCode}}.", "errorOutput": "Sortie d'erreur : {{output}}", "processExitedWithError": "Le processus Claude Code s'est terminé avec le code {{exitCode}}. Sortie d'erreur : {{output}}", "stoppedWithReason": "<PERSON> Code s'est arrêté pour la raison : {{reason}}", "apiKeyModelPlanMismatch": "Les clés API et les plans d'abonnement permettent différents modèles. Assurez-vous que le modèle sélectionné est inclus dans votre plan."}}, "warnings": {"no_terminal_content": "Aucun contenu de terminal sélectionné", "missing_task_files": "Les fichiers de cette tâche sont introuvables. Souhaitez-vous la supprimer de la liste des tâches ?", "auto_import_failed": "Échec de l'importation automatique des paramètres RooCode : {{error}}"}, "info": {"no_changes": "Aucun changement trouvé.", "clipboard_copy": "Prompt système copié dans le presse-papiers", "history_cleanup": "{{count}} tâche(s) avec des fichiers introuvables ont été supprimés de l'historique.", "custom_storage_path_set": "Chemin de stockage personnalisé dé<PERSON>i : {{path}}", "default_storage_path": "Retour au chemin de stockage par défaut", "settings_imported": "Paramètres importés avec succès.", "auto_import_success": "Paramètres RooCode importés automatiquement depuis {{filename}}", "share_link_copied": "Lien de partage copié dans le presse-papiers", "image_copied_to_clipboard": "URI de données d'image copiée dans le presse-papiers", "image_saved": "Image enregistrée dans {{path}}", "organization_share_link_copied": "Lien de partage d'organisation copié dans le presse-papiers !", "public_share_link_copied": "Lien de partage public copié dans le presse-papiers !", "mode_exported": "Mode '{{mode}}' exporté avec succès", "mode_imported": "Mode importé avec succès"}, "answers": {"yes": "O<PERSON>", "no": "Non", "remove": "<PERSON><PERSON><PERSON><PERSON>", "keep": "Conserver"}, "buttons": {"save": "Enregistrer", "edit": "Modifier"}, "tasks": {"canceled": "Erreur de tâche : Elle a été arrêtée et annulée par l'utilisateur.", "deleted": "Échec de la tâche : Elle a été arrêtée et supprimée par l'utilisateur.", "incomplete": "Tâche #{{taskNumber}} (Incomplète)", "no_messages": "Tâche #{{taskNumber}} (Aucun message)"}, "storage": {"prompt_custom_path": "Entrez le chemin de stockage personnalisé pour l'historique des conversations, laissez vide pour utiliser l'emplacement par défaut", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "Veuillez entrer un chemin absolu (ex. D:\\RooCodeStorage ou /home/<USER>/storage)", "enter_valid_path": "Veuillez entrer un chemin valide"}, "input": {"task_prompt": "Que doit faire Roo ?", "task_placeholder": "Écris ta tâche ici"}, "settings": {"providers": {"groqApiKey": "Clé API Groq", "getGroqApiKey": "Obtenir la clé API Groq", "claudeCode": {"pathLabel": "<PERSON><PERSON><PERSON> <PERSON>", "description": "Chemin optionnel vers votre CLI Claude Code. Par défaut 'claude' si non défini.", "placeholder": "Par dé<PERSON>ut : claude"}}}, "customModes": {"errors": {"yamlParseError": "YAML invalide dans le fichier .roomodes à la ligne {{line}}. Vérifie :\n• L'indentation correcte (utilise des espaces, pas de tabulations)\n• Les guillemets et crochets correspondants\n• La syntaxe YAML valide", "schemaValidationError": "Format invalide des modes personnalisés dans .roomodes :\n{{issues}}", "invalidFormat": "Format invalide des modes personnalisés. Assure-toi que tes paramètres suivent le format YAML correct.", "updateFailed": "Échec de la mise à jour du mode personnalisé : {{error}}", "deleteFailed": "Échec de la suppression du mode personnalisé : {{error}}", "resetFailed": "Échec de la réinitialisation des modes personnalisés : {{error}}", "modeNotFound": "Erreur d'écriture : Mode non trouvé", "noWorkspaceForProject": "Aucun dossier d'espace de travail trouvé pour le mode spécifique au projet"}, "scope": {"project": "projet", "global": "global"}}, "mdm": {"errors": {"cloud_auth_required": "Votre organisation nécessite une authentification Roo Code Cloud. Veuillez vous connecter pour continuer.", "organization_mismatch": "Vous devez être authentifié avec le compte Roo Code Cloud de votre organisation.", "verification_failed": "Impossible de vérifier l'authentification de l'organisation."}}, "prompts": {"deleteMode": {"title": "Supprimer le mode personnalisé", "description": "Êtes-vous sûr de vouloir supprimer ce mode {{scope}} ? Cela supprimera également le dossier de règles associé à l'adresse : {{rulesFolderPath}}", "descriptionNoRules": "Êtes-vous sûr de vouloir supprimer ce mode personnalisé ?", "confirm": "<PERSON><PERSON><PERSON><PERSON>"}}}
{"input": {"task_prompt": "O que você quer que o Roo faça?", "task_placeholder": "Digite sua tarefa aqui"}, "extension": {"name": "Roo Code", "description": "Uma equipe completa de desenvolvedores com IA em seu editor."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "welcome": "<PERSON><PERSON>-vind<PERSON>(a), {{name}}! Você tem {{count}} notificações.", "items": {"zero": "Nenhum item", "one": "Um item", "other": "{{count}} itens"}, "confirmation": {"reset_state": "Tem certeza de que deseja redefinir todo o estado e armazenamento secreto na extensão? Isso não pode ser desfeito.", "delete_config_profile": "Tem certeza de que deseja excluir este perfil de configuração?", "delete_custom_mode_with_rules": "Tem certeza de que deseja excluir este modo {scope}?\n\n<PERSON>so também excluirá a pasta de regras associada em:\n{rulesFolderPath}", "delete_message": "O que você gostaria de excluir?", "delete_just_this_message": "Apenas esta mensagem", "delete_this_and_subsequent": "<PERSON><PERSON> e todas as mensagens subsequentes", "edit_warning": "Editar esta mensagem excluirá todas as mensagens subsequentes na conversa. Deseja continuar?", "proceed": "<PERSON><PERSON><PERSON><PERSON>"}, "errors": {"invalid_data_uri": "Formato de URI de dados inválido", "error_copying_image": "Erro ao copiar imagem: {{errorMessage}}", "error_saving_image": "Erro ao salvar imagem: {{errorMessage}}", "error_opening_image": "Erro ao abrir imagem: {{error}}", "could_not_open_file": "Não foi possível abrir o arquivo: {{errorMessage}}", "could_not_open_file_generic": "Não foi possível abrir o arquivo!", "checkpoint_timeout": "Tempo esgotado ao tentar restaurar o ponto de verificação.", "checkpoint_failed": "Falha ao restaurar o ponto de verificação.", "no_workspace": "Por favor, abra primeiro uma pasta de projeto", "update_support_prompt": "Falha ao atualizar o prompt de suporte", "reset_support_prompt": "Falha ao redefinir o prompt de suporte", "enhance_prompt": "Falha ao aprimorar o prompt", "get_system_prompt": "Falha ao obter o prompt do sistema", "search_commits": "<PERSON><PERSON><PERSON> ao pesquisar commits", "save_api_config": "Falha ao salvar a configuração da API", "create_api_config": "Falha ao criar a configuração da API", "rename_api_config": "Falha ao renomear a configuração da API", "load_api_config": "Falha ao carregar a configuração da API", "delete_api_config": "Falha ao excluir a configuração da API", "list_api_config": "Falha ao obter a lista de configurações da API", "update_server_timeout": "Falha ao atualizar o tempo limite do servidor", "hmr_not_running": "O servidor de desenvolvimento local não está em execução, o HMR não funcionará. Por favor, execute 'npm run dev' antes de iniciar a extensão para habilitar o HMR.", "retrieve_current_mode": "Erro ao recuperar o modo atual do estado.", "failed_delete_repo": "Falha ao excluir o repositório ou ramificação associada: {{error}}", "failed_remove_directory": "Falha ao remover o diretório de tarefas: {{error}}", "custom_storage_path_unusable": "O caminho de armazenamento personalizado \"{{path}}\" não pode ser usado, será usado o caminho padrão", "cannot_access_path": "Não é possível acessar o caminho {{path}}: {{error}}", "settings_import_failed": "Falha ao importar configurações: {{error}}", "mistake_limit_guidance": "<PERSON><PERSON> pode indicar uma falha no processo de pensamento do modelo ou incapacidade de usar uma ferramenta adequadamente, o que pode ser mitigado com orientação do usuário (ex. \"Tente dividir a tarefa em etapas menores\").", "violated_organization_allowlist": "Falha ao executar a tarefa: o perfil atual não é compatível com as configurações da sua organização", "condense_failed": "Falha ao condensar o contexto", "condense_not_enough_messages": "Não há mensagens suficientes para condensar o contexto", "condensed_recently": "O contexto foi condensado recentemente; pulando esta tentativa", "condense_handler_invalid": "O manipulador de API para condensar o contexto é inválido", "condense_context_grew": "O tamanho do contexto aumentou durante a condensação; pulando esta tentativa", "url_timeout": "O site demorou muito para carregar (timeout). <PERSON><PERSON> pode ser devido a uma conexão lenta, site pesado ou temporariamente indisponível. Você pode tentar novamente mais tarde ou verificar se a URL está correta.", "url_not_found": "O endereço do site não pôde ser encontrado. Verifique se a URL está correta e tente novamente.", "no_internet": "Sem conexão com a internet. Verifique sua conexão de rede e tente novamente.", "url_forbidden": "O acesso a este site está proibido. O site pode bloquear acesso automatizado ou exigir autenticação.", "url_page_not_found": "A página não foi encontrada. Verifique se a URL está correta.", "url_fetch_failed": "Falha ao buscar conteúdo da URL: {{error}}", "url_fetch_error_with_url": "Erro ao buscar conteúdo para {{url}}: {{error}}", "share_task_failed": "Falha ao compartilhar tarefa", "share_no_active_task": "Nenhuma tarefa ativa para compartilhar", "share_auth_required": "Autenticação necessária. Faça login para compartilhar tarefas.", "share_not_enabled": "O compartilhamento de tarefas não está habilitado para esta organização.", "share_task_not_found": "Tarefa não encontrada ou acesso negado.", "mode_import_failed": "Falha ao importar o modo: {{error}}", "delete_rules_folder_failed": "Falha ao excluir pasta de regras: {{rulesFolderPath}}. Erro: {{error}}", "claudeCode": {"processExited": "O processo Claude Code saiu com código {{exitCode}}.", "errorOutput": "<PERSON><PERSON><PERSON> de er<PERSON>: {{output}}", "processExitedWithError": "O processo Claude Code saiu com código {{exitCode}}. <PERSON><PERSON><PERSON> de erro: {{output}}", "stoppedWithReason": "<PERSON> Code parou pela razão: {{reason}}", "apiKeyModelPlanMismatch": "Chaves de API e planos de assinatura permitem modelos diferentes. Certifique-se de que o modelo selecionado esteja incluído no seu plano."}}, "warnings": {"no_terminal_content": "Nenhum conteúdo do terminal selecionado", "missing_task_files": "Os arquivos desta tarefa estão faltando. Deseja removê-la da lista de tarefas?", "auto_import_failed": "Falha ao importar automaticamente as configurações do RooCode: {{error}}"}, "info": {"no_changes": "Nenhuma alteração encontrada.", "clipboard_copy": "Prompt do sistema copiado com sucesso para a área de transferência", "history_cleanup": "{{count}} tarefa(s) com arquivos ausentes foram limpas do histórico.", "custom_storage_path_set": "Caminho de armazenamento personalizado definido: {{path}}", "default_storage_path": "Retornado ao caminho de armazenamento padrão", "settings_imported": "Configurações importadas com sucesso.", "auto_import_success": "Configurações do RooCode importadas automaticamente de {{filename}}", "share_link_copied": "Link de compartilhamento copiado para a área de transferência", "image_copied_to_clipboard": "URI de dados da imagem copiada para a área de transferência", "image_saved": "Imagem salva em {{path}}", "organization_share_link_copied": "Link de compartilhamento da organização copiado para a área de transferência!", "public_share_link_copied": "Link de compartilhamento público copiado para a área de transferência!", "mode_exported": "Modo '{{mode}}' exportado com sucesso", "mode_imported": "Modo importado com sucesso"}, "answers": {"yes": "<PERSON>m", "no": "Não", "remove": "Remover", "keep": "<PERSON><PERSON>"}, "buttons": {"save": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>"}, "tasks": {"canceled": "Erro na tarefa: Foi interrompida e cancelada pelo usuário.", "deleted": "Falha na tarefa: Foi interrompida e excluída pelo usuário.", "incomplete": "Tarefa #{{taskNumber}} (Incompleta)", "no_messages": "Tarefa #{{taskNumber}} (Sem mensagens)"}, "storage": {"prompt_custom_path": "Digite o caminho de armazenamento personalizado para o histórico de conversas, deixe em branco para usar o local padrão", "path_placeholder": "D:\\RooCodeStorage", "enter_absolute_path": "Por favor, digite um caminho absoluto (ex: D:\\RooCodeStorage ou /home/<USER>/storage)", "enter_valid_path": "Por favor, digite um caminho válido"}, "settings": {"providers": {"groqApiKey": "Chave de API Groq", "getGroqApiKey": "Obter chave de API Groq", "claudeCode": {"pathLabel": "<PERSON>in<PERSON> <PERSON>", "description": "Caminho opcional para sua CLI do Claude Code. Padrão 'claude' se não for definido.", "placeholder": "Padrão: claude"}}}, "customModes": {"errors": {"yamlParseError": "YAML inválido no arquivo .roomodes na linha {{line}}. Verifique:\n• Indentação correta (use espaços, não tabs)\n• Aspas e colchetes correspondentes\n• Sintaxe YAML válida", "schemaValidationError": "Formato de modos personalizados inválido em .roomodes:\n{{issues}}", "invalidFormat": "Formato de modos personalizados inválido. Certifique-se de que suas configurações seguem o formato YAML correto.", "updateFailed": "Falha ao atualizar modo personalizado: {{error}}", "deleteFailed": "Falha ao excluir modo personalizado: {{error}}", "resetFailed": "Falha ao redefinir modos personalizados: {{error}}", "modeNotFound": "Erro de escrita: Modo não encontrado", "noWorkspaceForProject": "Nenhuma pasta de workspace encontrada para modo específico do projeto"}, "scope": {"project": "projeto", "global": "global"}}, "mdm": {"errors": {"cloud_auth_required": "Sua organização requer autenticação do Roo Code Cloud. Faça login para continuar.", "organization_mismatch": "Você deve estar autenticado com a conta Roo Code Cloud da sua organização.", "verification_failed": "Não foi possível verificar a autenticação da organização."}}, "prompts": {"deleteMode": {"title": "Excluir <PERSON> Personalizado", "description": "Tem certeza de que deseja excluir este modo {{scope}}? Isso também excluirá a pasta de regras associada em: {{rulesFolderPath}}", "descriptionNoRules": "Tem certeza de que deseja excluir este modo personalizado?", "confirm": "Excluir"}}}
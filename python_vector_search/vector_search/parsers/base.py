"""Base parser interface."""

from abc import ABC, abstractmethod
from typing import List, Optional
from pathlib import Path

from ..models import CodeBlock
from ..constants import SUPPORTED_EXTENSIONS


class BaseParser(ABC):
    """Abstract base class for code parsers."""
    
    @abstractmethod
    async def parse_file(
        self, 
        file_path: str, 
        content: Optional[str] = None,
        file_hash: Optional[str] = None
    ) -> List[CodeBlock]:
        """Parse a file into code blocks.
        
        Args:
            file_path: Path to the file to parse
            content: Optional file content (if not provided, will read from file)
            file_hash: Optional file hash for caching
            
        Returns:
            List of parsed code blocks
        """
        pass
    
    @abstractmethod
    def is_supported_language(self, file_path: str) -> bool:
        """Check if the file extension is supported.
        
        Args:
            file_path: Path to check
            
        Returns:
            True if supported, False otherwise
        """
        pass
    
    def get_file_extension(self, file_path: str) -> str:
        """Get the file extension.
        
        Args:
            file_path: File path
            
        Returns:
            File extension (including the dot)
        """
        return Path(file_path).suffix.lower()
    
    def create_file_hash(self, content: str) -> str:
        """Create a hash for file content.
        
        Args:
            content: File content
            
        Returns:
            SHA256 hash of the content
        """
        import hashlib
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    def is_supported_extension(self, extension: str) -> bool:
        """Check if an extension is supported.
        
        Args:
            extension: File extension (with or without dot)
            
        Returns:
            True if supported, False otherwise
        """
        if not extension.startswith('.'):
            extension = '.' + extension
        return extension.lower() in SUPPORTED_EXTENSIONS

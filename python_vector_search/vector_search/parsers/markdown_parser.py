"""Markdown-specific parser."""

import hashlib
from typing import List, Optional, Set
from .base import BaseParser
from ..models import CodeBlock
from ..constants import MIN_BLOCK_CHARS


class MarkdownParser(BaseParser):
    """Parser specifically for Markdown files."""
    
    def __init__(self):
        """Initialize the markdown parser."""
        pass
    
    async def parse_file(
        self, 
        file_path: str, 
        content: Optional[str] = None,
        file_hash: Optional[str] = None
    ) -> List[CodeBlock]:
        """Parse a markdown file into code blocks.
        
        Args:
            file_path: Path to the file to parse
            content: Optional file content
            file_hash: Optional file hash
            
        Returns:
            List of parsed code blocks
        """
        if not self.is_supported_language(file_path):
            return []
        
        # Read content if not provided
        if content is None:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except Exception:
                return []
        
        # Create file hash if not provided
        if file_hash is None:
            file_hash = self.create_file_hash(content)
        
        return self._parse_markdown_content(file_path, content, file_hash)
    
    def _parse_markdown_content(
        self,
        file_path: str,
        content: str,
        file_hash: str
    ) -> List[CodeBlock]:
        """Parse markdown content into code blocks.
        
        Args:
            file_path: File path
            content: File content
            file_hash: File hash
            
        Returns:
            List of code blocks
        """
        lines = content.split('\n')
        blocks = []
        seen_segment_hashes: Set[str] = set()
        
        # Track state
        in_code_block = False
        current_code_lines = []
        code_block_start_line = 0
        code_block_language = ""
        
        # Track current section
        current_section_lines = []
        section_start_line = 1
        current_header_level = 0
        
        for i, line in enumerate(lines, 1):
            stripped_line = line.strip()
            
            # Handle code blocks
            if stripped_line.startswith('```'):
                if in_code_block:
                    # End of code block
                    if current_code_lines:
                        code_content = '\n'.join(current_code_lines)
                        if len(code_content.strip()) >= MIN_BLOCK_CHARS:
                            self._add_code_block(
                                blocks, file_path, file_hash, code_content,
                                f"code_block_{code_block_language}" if code_block_language else "code_block",
                                code_block_start_line, i - 1, seen_segment_hashes
                            )
                    current_code_lines = []
                    in_code_block = False
                    code_block_language = ""
                else:
                    # Start of code block
                    in_code_block = True
                    code_block_start_line = i + 1
                    # Extract language if specified
                    if len(stripped_line) > 3:
                        code_block_language = stripped_line[3:].strip()
                continue
            
            if in_code_block:
                current_code_lines.append(line)
                continue
            
            # Handle headers
            if stripped_line.startswith('#'):
                # Finalize previous section
                if current_section_lines:
                    section_content = '\n'.join(current_section_lines).strip()
                    if len(section_content) >= MIN_BLOCK_CHARS:
                        self._add_code_block(
                            blocks, file_path, file_hash, section_content,
                            f"section_h{current_header_level}", section_start_line, i - 1, seen_segment_hashes
                        )
                
                # Start new section
                header_level = len(stripped_line) - len(stripped_line.lstrip('#'))
                current_header_level = header_level
                current_section_lines = [line]
                section_start_line = i
            else:
                # Add to current section
                current_section_lines.append(line)
        
        # Finalize last section
        if current_section_lines:
            section_content = '\n'.join(current_section_lines).strip()
            if len(section_content) >= MIN_BLOCK_CHARS:
                self._add_code_block(
                    blocks, file_path, file_hash, section_content,
                    f"section_h{current_header_level}", section_start_line, len(lines), seen_segment_hashes
                )
        
        # Finalize last code block if still open
        if in_code_block and current_code_lines:
            code_content = '\n'.join(current_code_lines)
            if len(code_content.strip()) >= MIN_BLOCK_CHARS:
                self._add_code_block(
                    blocks, file_path, file_hash, code_content,
                    f"code_block_{code_block_language}" if code_block_language else "code_block",
                    code_block_start_line, len(lines), seen_segment_hashes
                )
        
        return blocks
    
    def _add_code_block(
        self,
        blocks: List[CodeBlock],
        file_path: str,
        file_hash: str,
        content: str,
        block_type: str,
        start_line: int,
        end_line: int,
        seen_segment_hashes: Set[str]
    ):
        """Add a code block to the list.
        
        Args:
            blocks: List to append to
            file_path: File path
            file_hash: File hash
            content: Block content
            block_type: Type of block
            start_line: Start line
            end_line: End line
            seen_segment_hashes: Set of seen segment hashes
        """
        content_preview = content[:100]
        segment_hash = hashlib.sha256(
            f"{file_path}-{start_line}-{end_line}-{len(content)}-{content_preview}".encode()
        ).hexdigest()
        
        if segment_hash not in seen_segment_hashes:
            seen_segment_hashes.add(segment_hash)
            
            # Extract identifier for headers
            identifier = None
            if block_type.startswith("section_h") and content.strip().startswith('#'):
                first_line = content.split('\n')[0].strip()
                identifier = first_line.lstrip('#').strip()
            
            blocks.append(CodeBlock(
                file_path=file_path,
                identifier=identifier,
                type=block_type,
                start_line=start_line,
                end_line=end_line,
                content=content,
                segment_hash=segment_hash,
                file_hash=file_hash
            ))
    
    def is_supported_language(self, file_path: str) -> bool:
        """Check if the file is a markdown file.
        
        Args:
            file_path: Path to check
            
        Returns:
            True if markdown file, False otherwise
        """
        extension = self.get_file_extension(file_path)
        return extension in ['.md', '.markdown']

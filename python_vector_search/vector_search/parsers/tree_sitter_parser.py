"""Tree-sitter based code parser."""

import hashlib
from typing import List, Optional, Dict, Set
from pathlib import Path
import tree_sitter
from tree_sitter import Language, Parser

from .base import BaseParser
from ..models import CodeBlock
from ..constants import (
    LANGUAGE_MAPPINGS, 
    MAX_BLOCK_CHARS, 
    MIN_BLOCK_CHARS,
    MIN_CHUNK_REMAINDER_CHARS,
    MAX_CHARS_TOLERANCE_FACTOR
)


class TreeSitterParser(BaseParser):
    """Tree-sitter based parser for multiple programming languages."""
    
    def __init__(self):
        """Initialize the parser."""
        self.parsers: Dict[str, Parser] = {}
        self.languages: Dict[str, Language] = {}
        self._load_languages()
    
    def _load_languages(self):
        """Load tree-sitter languages."""
        # This would need to be implemented based on available tree-sitter bindings
        # For now, we'll implement a basic fallback
        pass
    
    async def parse_file(
        self, 
        file_path: str, 
        content: Optional[str] = None,
        file_hash: Optional[str] = None
    ) -> List[CodeBlock]:
        """Parse a file into code blocks.
        
        Args:
            file_path: Path to the file to parse
            content: Optional file content
            file_hash: Optional file hash
            
        Returns:
            List of parsed code blocks
        """
        if not self.is_supported_language(file_path):
            return []
        
        # Read content if not provided
        if content is None:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except Exception:
                return []
        
        # Create file hash if not provided
        if file_hash is None:
            file_hash = self.create_file_hash(content)
        
        return await self._parse_content(file_path, content, file_hash)
    
    async def _parse_content(
        self, 
        file_path: str, 
        content: str, 
        file_hash: str
    ) -> List[CodeBlock]:
        """Parse file content into code blocks.
        
        Args:
            file_path: File path
            content: File content
            file_hash: File hash
            
        Returns:
            List of code blocks
        """
        extension = self.get_file_extension(file_path)
        seen_segment_hashes: Set[str] = set()
        
        # Handle markdown files specially
        if extension in ['.md', '.markdown']:
            return self._parse_markdown_content(file_path, content, file_hash, seen_segment_hashes)
        
        # Try to get language parser
        language_name = LANGUAGE_MAPPINGS.get(extension)
        if not language_name or language_name not in self.parsers:
            # Fallback to simple chunking if no parser available
            if len(content) >= MIN_BLOCK_CHARS:
                return self._perform_fallback_chunking(file_path, content, file_hash, seen_segment_hashes)
            return []
        
        # Parse with tree-sitter
        parser = self.parsers[language_name]
        tree = parser.parse(content.encode('utf-8'))
        
        if not tree or not tree.root_node:
            # Fallback to simple chunking
            if len(content) >= MIN_BLOCK_CHARS:
                return self._perform_fallback_chunking(file_path, content, file_hash, seen_segment_hashes)
            return []
        
        # Extract code blocks from AST
        return self._extract_blocks_from_ast(
            file_path, content, file_hash, tree.root_node, seen_segment_hashes
        )
    
    def _extract_blocks_from_ast(
        self,
        file_path: str,
        content: str,
        file_hash: str,
        root_node,
        seen_segment_hashes: Set[str]
    ) -> List[CodeBlock]:
        """Extract code blocks from AST.
        
        Args:
            file_path: File path
            content: File content
            file_hash: File hash
            root_node: Root AST node
            seen_segment_hashes: Set of seen segment hashes
            
        Returns:
            List of code blocks
        """
        results = []
        
        # Simple traversal - in a real implementation, you'd use tree-sitter queries
        def traverse(node):
            # Check if this is a function, class, or other interesting node
            if self._is_interesting_node(node):
                node_text = content[node.start_byte:node.end_byte]
                
                if MIN_BLOCK_CHARS <= len(node_text) <= MAX_BLOCK_CHARS:
                    # Create code block
                    start_line = node.start_point[0] + 1
                    end_line = node.end_point[0] + 1
                    
                    # Try to extract identifier
                    identifier = self._extract_identifier(node, content)
                    
                    # Create segment hash
                    content_preview = node_text[:100]
                    segment_hash = hashlib.sha256(
                        f"{file_path}-{start_line}-{end_line}-{len(node_text)}-{content_preview}".encode()
                    ).hexdigest()
                    
                    if segment_hash not in seen_segment_hashes:
                        seen_segment_hashes.add(segment_hash)
                        results.append(CodeBlock(
                            file_path=file_path,
                            identifier=identifier,
                            type=node.type,
                            start_line=start_line,
                            end_line=end_line,
                            content=node_text,
                            segment_hash=segment_hash,
                            file_hash=file_hash
                        ))
                elif len(node_text) > MAX_BLOCK_CHARS:
                    # Chunk large blocks
                    chunks = self._chunk_large_block(
                        file_path, node_text, file_hash, node.type, 
                        node.start_point[0] + 1, seen_segment_hashes
                    )
                    results.extend(chunks)
            
            # Recursively traverse children
            for child in node.children:
                traverse(child)
        
        traverse(root_node)
        return results
    
    def _is_interesting_node(self, node) -> bool:
        """Check if a node is interesting for indexing.
        
        Args:
            node: AST node
            
        Returns:
            True if interesting, False otherwise
        """
        # Common node types that are interesting across languages
        interesting_types = {
            'function_definition', 'function_declaration', 'function',
            'class_definition', 'class_declaration', 'class',
            'method_definition', 'method_declaration', 'method',
            'interface_declaration', 'interface',
            'struct_declaration', 'struct',
            'enum_declaration', 'enum',
            'type_declaration', 'type_alias',
            'variable_declaration', 'const_declaration',
            'import_declaration', 'import_statement',
            'export_declaration', 'export_statement'
        }
        return node.type in interesting_types
    
    def _extract_identifier(self, node, content: str) -> Optional[str]:
        """Extract identifier from a node.

        Args:
            node: AST node
            content: File content

        Returns:
            Identifier string or None
        """
        # Try to find name field or identifier child
        for child in node.children:
            if child.type == 'identifier' or 'name' in child.type:
                return content[child.start_byte:child.end_byte]
        return None

    def _chunk_large_block(
        self,
        file_path: str,
        content: str,
        file_hash: str,
        block_type: str,
        start_line: int,
        seen_segment_hashes: Set[str]
    ) -> List[CodeBlock]:
        """Chunk a large block into smaller pieces.

        Args:
            file_path: File path
            content: Block content
            file_hash: File hash
            block_type: Type of the block
            start_line: Starting line number
            seen_segment_hashes: Set of seen segment hashes

        Returns:
            List of code block chunks
        """
        lines = content.split('\n')
        return self._chunk_text_by_lines(
            lines, file_path, file_hash, block_type, seen_segment_hashes, start_line
        )

    def _chunk_text_by_lines(
        self,
        lines: List[str],
        file_path: str,
        file_hash: str,
        chunk_type: str,
        seen_segment_hashes: Set[str],
        base_start_line: int = 1
    ) -> List[CodeBlock]:
        """Chunk text by lines, avoiding tiny remainders.

        Args:
            lines: List of lines
            file_path: File path
            file_hash: File hash
            chunk_type: Type of chunk
            seen_segment_hashes: Set of seen segment hashes
            base_start_line: Base starting line number

        Returns:
            List of code blocks
        """
        chunks = []
        current_chunk_lines = []
        current_chunk_length = 0
        chunk_start_line_index = 0
        effective_max_chars = MAX_BLOCK_CHARS * MAX_CHARS_TOLERANCE_FACTOR

        def finalize_chunk(end_line_index: int):
            if current_chunk_length >= MIN_BLOCK_CHARS and current_chunk_lines:
                chunk_content = '\n'.join(current_chunk_lines)
                start_line = base_start_line + chunk_start_line_index
                end_line = base_start_line + end_line_index
                content_preview = chunk_content[:100]
                segment_hash = hashlib.sha256(
                    f"{file_path}-{start_line}-{end_line}-{len(chunk_content)}-{content_preview}".encode()
                ).hexdigest()

                if segment_hash not in seen_segment_hashes:
                    seen_segment_hashes.add(segment_hash)
                    chunks.append(CodeBlock(
                        file_path=file_path,
                        identifier=None,
                        type=chunk_type,
                        start_line=start_line,
                        end_line=end_line,
                        content=chunk_content,
                        segment_hash=segment_hash,
                        file_hash=file_hash
                    ))

        for i, line in enumerate(lines):
            line_length = len(line) + 1  # +1 for newline

            # Check for oversized lines
            if line_length > MAX_BLOCK_CHARS:
                # Finalize current chunk if any
                if current_chunk_lines:
                    finalize_chunk(i - 1)
                    current_chunk_lines = []
                    current_chunk_length = 0
                    chunk_start_line_index = i + 1

                # Create segments for oversized line
                self._create_line_segments(
                    line, file_path, file_hash, chunk_type,
                    base_start_line + i, seen_segment_hashes, chunks
                )
                continue

            # Check if adding this line would exceed limit
            if current_chunk_length + line_length > effective_max_chars and current_chunk_lines:
                finalize_chunk(i - 1)
                current_chunk_lines = [line]
                current_chunk_length = line_length
                chunk_start_line_index = i
            else:
                current_chunk_lines.append(line)
                current_chunk_length += line_length

        # Finalize last chunk
        if current_chunk_lines:
            finalize_chunk(len(lines) - 1)

        return chunks

    def _create_line_segments(
        self,
        line: str,
        file_path: str,
        file_hash: str,
        chunk_type: str,
        line_number: int,
        seen_segment_hashes: Set[str],
        chunks: List[CodeBlock]
    ):
        """Create segments for an oversized line.

        Args:
            line: The oversized line
            file_path: File path
            file_hash: File hash
            chunk_type: Type of chunk
            line_number: Line number
            seen_segment_hashes: Set of seen segment hashes
            chunks: List to append chunks to
        """
        start_char = 0
        while start_char < len(line):
            end_char = min(start_char + MAX_BLOCK_CHARS, len(line))
            segment = line[start_char:end_char]

            if len(segment) >= MIN_BLOCK_CHARS:
                segment_preview = segment[:100]
                segment_hash = hashlib.sha256(
                    f"{file_path}-{line_number}-{line_number}-{start_char}-{len(segment)}-{segment_preview}".encode()
                ).hexdigest()

                if segment_hash not in seen_segment_hashes:
                    seen_segment_hashes.add(segment_hash)
                    chunks.append(CodeBlock(
                        file_path=file_path,
                        identifier=None,
                        type=f"{chunk_type}_segment",
                        start_line=line_number,
                        end_line=line_number,
                        content=segment,
                        segment_hash=segment_hash,
                        file_hash=file_hash
                    ))

            start_char = end_char

    def _perform_fallback_chunking(
        self,
        file_path: str,
        content: str,
        file_hash: str,
        seen_segment_hashes: Set[str]
    ) -> List[CodeBlock]:
        """Perform fallback chunking when no parser is available.

        Args:
            file_path: File path
            content: File content
            file_hash: File hash
            seen_segment_hashes: Set of seen segment hashes

        Returns:
            List of code blocks
        """
        lines = content.split('\n')
        return self._chunk_text_by_lines(
            lines, file_path, file_hash, "fallback", seen_segment_hashes
        )

    def _parse_markdown_content(
        self,
        file_path: str,
        content: str,
        file_hash: str,
        seen_segment_hashes: Set[str]
    ) -> List[CodeBlock]:
        """Parse markdown content.

        Args:
            file_path: File path
            content: File content
            file_hash: File hash
            seen_segment_hashes: Set of seen segment hashes

        Returns:
            List of code blocks
        """
        # Simple markdown parsing - extract code blocks and headers
        lines = content.split('\n')
        blocks = []
        current_block_lines = []
        current_block_type = "text"
        in_code_block = False
        start_line = 1

        for i, line in enumerate(lines, 1):
            if line.strip().startswith('```'):
                if in_code_block:
                    # End of code block
                    if current_block_lines:
                        block_content = '\n'.join(current_block_lines)
                        if len(block_content) >= MIN_BLOCK_CHARS:
                            self._add_markdown_block(
                                blocks, file_path, file_hash, block_content,
                                "code_block", start_line, i - 1, seen_segment_hashes
                            )
                    current_block_lines = []
                    in_code_block = False
                else:
                    # Start of code block
                    in_code_block = True
                    start_line = i + 1
            elif in_code_block:
                current_block_lines.append(line)
            elif line.strip().startswith('#'):
                # Header
                if len(line.strip()) >= MIN_BLOCK_CHARS:
                    self._add_markdown_block(
                        blocks, file_path, file_hash, line.strip(),
                        "header", i, i, seen_segment_hashes
                    )

        return blocks

    def _add_markdown_block(
        self,
        blocks: List[CodeBlock],
        file_path: str,
        file_hash: str,
        content: str,
        block_type: str,
        start_line: int,
        end_line: int,
        seen_segment_hashes: Set[str]
    ):
        """Add a markdown block.

        Args:
            blocks: List to append to
            file_path: File path
            file_hash: File hash
            content: Block content
            block_type: Type of block
            start_line: Start line
            end_line: End line
            seen_segment_hashes: Set of seen segment hashes
        """
        content_preview = content[:100]
        segment_hash = hashlib.sha256(
            f"{file_path}-{start_line}-{end_line}-{len(content)}-{content_preview}".encode()
        ).hexdigest()

        if segment_hash not in seen_segment_hashes:
            seen_segment_hashes.add(segment_hash)
            blocks.append(CodeBlock(
                file_path=file_path,
                identifier=None,
                type=block_type,
                start_line=start_line,
                end_line=end_line,
                content=content,
                segment_hash=segment_hash,
                file_hash=file_hash
            ))

    def is_supported_language(self, file_path: str) -> bool:
        """Check if the file extension is supported.

        Args:
            file_path: Path to check

        Returns:
            True if supported, False otherwise
        """
        extension = self.get_file_extension(file_path)
        return self.is_supported_extension(extension)

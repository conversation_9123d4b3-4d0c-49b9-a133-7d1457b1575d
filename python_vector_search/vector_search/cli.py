#!/usr/bin/env python3
"""Command-line interface for the vector search package."""

import asyncio
import argparse
import sys
import os
from pathlib import Path

from .engine import VectorSearchEngine
from .config import Config
from .models import EmbedderProvider


async def index_command(args):
    """Handle index command."""
    print(f"Indexing directory: {args.directory}")
    
    # Create configuration
    config = Config(
        embedder_provider=EmbedderProvider(args.embedder),
        openai_api_key=args.openai_key or os.getenv("OPENAI_API_KEY"),
        ollama_base_url=args.ollama_url,
        gemini_api_key=args.gemini_key or os.getenv("GEMINI_API_KEY"),
        model_id=args.model,
        model_dimension=args.dimension,
        lancedb_path=args.db_path,
        search_min_score=args.min_score,
        search_max_results=args.max_results
    )
    
    engine = VectorSearchEngine(config)
    
    try:
        await engine.initialize()
        
        def progress_callback(processed, total, current_file):
            if args.verbose:
                print(f"Processing {processed}/{total}: {current_file}")
            elif processed % 10 == 0 or processed == total:
                print(f"Progress: {processed}/{total} files")
        
        summary = await engine.index_directory(
            args.directory,
            progress_callback=progress_callback
        )
        
        print(f"\n✓ Indexing complete!")
        print(f"  Total files: {summary.total_files}")
        print(f"  Processed files: {summary.processed_files}")
        print(f"  Indexed blocks: {summary.indexed_blocks}")
        print(f"  Processing time: {summary.processing_time:.2f}s")
        
        if summary.errors:
            print(f"  Errors: {len(summary.errors)}")
    
    except Exception as e:
        print(f"Error during indexing: {e}")
        sys.exit(1)
    finally:
        await engine.close()


async def search_command(args):
    """Handle search command."""
    print(f"Searching for: {args.query}")
    
    # Create configuration
    config = Config(
        embedder_provider=EmbedderProvider(args.embedder),
        openai_api_key=args.openai_key or os.getenv("OPENAI_API_KEY"),
        ollama_base_url=args.ollama_url,
        gemini_api_key=args.gemini_key or os.getenv("GEMINI_API_KEY"),
        model_id=args.model,
        model_dimension=args.dimension,
        lancedb_path=args.db_path,
        search_min_score=args.min_score,
        search_max_results=args.max_results
    )
    
    engine = VectorSearchEngine(config)
    
    try:
        await engine.initialize()
        
        # Parse file extensions if provided
        file_extensions = None
        if args.extensions:
            file_extensions = [ext.strip() for ext in args.extensions.split(',')]
        
        # Perform search
        results = await engine.search(
            query=args.query,
            directory_prefix=args.directory_filter,
            min_score=args.min_score,
            max_results=args.max_results,
            file_extensions=file_extensions
        )
        
        if not results:
            print("No results found.")
            return
        
        print(f"\nFound {len(results)} results:")
        print("-" * 80)
        
        for i, result in enumerate(results, 1):
            print(f"{i}. {result.file_path}:{result.start_line}-{result.end_line}")
            print(f"   Score: {result.score:.3f}")
            
            if args.show_code:
                # Show code snippet
                lines = result.code_chunk.split('\n')[:3]
                for line in lines:
                    print(f"   | {line}")
            print()
    
    except Exception as e:
        print(f"Error during search: {e}")
        sys.exit(1)
    finally:
        await engine.close()


def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(
        description="Vector Search CLI - Index and search code repositories"
    )
    
    # Global arguments
    parser.add_argument("--embedder", choices=["openai", "ollama", "openai_compatible", "gemini"],
                       default="openai", help="Embedder provider to use")
    parser.add_argument("--openai-key", help="OpenAI API key")
    parser.add_argument("--ollama-url", default="http://localhost:11434", help="Ollama base URL")
    parser.add_argument("--gemini-key", help="Gemini API key")
    parser.add_argument("--model", help="Model ID to use")
    parser.add_argument("--dimension", type=int, help="Vector dimension")
    parser.add_argument("--db-path", default="./vector_db", help="Database path")
    parser.add_argument("--min-score", type=float, default=0.3, help="Minimum search score")
    parser.add_argument("--max-results", type=int, default=20, help="Maximum search results")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    
    # Subcommands
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Index command
    index_parser = subparsers.add_parser("index", help="Index a directory")
    index_parser.add_argument("directory", help="Directory to index")
    
    # Search command
    search_parser = subparsers.add_parser("search", help="Search indexed code")
    search_parser.add_argument("query", help="Search query")
    search_parser.add_argument("--directory-filter", help="Filter by directory prefix")
    search_parser.add_argument("--extensions", help="Filter by file extensions (comma-separated)")
    search_parser.add_argument("--show-code", action="store_true", help="Show code snippets")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # Run the appropriate command
    if args.command == "index":
        asyncio.run(index_command(args))
    elif args.command == "search":
        asyncio.run(search_command(args))


if __name__ == "__main__":
    main()

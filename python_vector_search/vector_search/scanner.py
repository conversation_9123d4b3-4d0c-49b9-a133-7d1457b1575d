"""File scanner for processing directories and files."""

import asyncio
import time
from pathlib import Path
from typing import List, Set, Dict, Optional, Callable, Any
import pathspec
from tqdm.asyncio import tqdm

from .models import Code<PERSON>lock, FileProcessingResult, BatchProcessingSummary
from .parsers.base import BaseParser
from .embedders.base import BaseEmbedder
from .storage.base import BaseVectorStore
from .cache import CacheManager
from .constants import (
    SUPPORTED_EXTENSIONS,
    MAX_FILE_SIZE_BYTES,
    BATCH_SEGMENT_THRESHOLD,
    PARSING_CONCURRENCY
)


class FileScanner:
    """Scans directories and processes files for indexing."""
    
    def __init__(
        self,
        parser: BaseParser,
        embedder: BaseEmbedder,
        vector_store: BaseVectorStore,
        cache_manager: CacheManager,
        ignore_patterns: Optional[List[str]] = None
    ):
        """Initialize file scanner.
        
        Args:
            parser: Code parser instance
            embedder: Embedder instance
            vector_store: Vector store instance
            cache_manager: Cache manager instance
            ignore_patterns: List of ignore patterns (gitignore style)
        """
        self.parser = parser
        self.embedder = embedder
        self.vector_store = vector_store
        self.cache_manager = cache_manager
        
        # Setup ignore patterns
        self.ignore_spec = None
        if ignore_patterns:
            self.ignore_spec = pathspec.PathSpec.from_lines('gitwildmatch', ignore_patterns)
    
    async def scan_directory(
        self,
        directory_path: str,
        progress_callback: Optional[Callable[[int, int, str], None]] = None,
        block_callback: Optional[Callable[[int], None]] = None
    ) -> BatchProcessingSummary:
        """Scan a directory and process all supported files.
        
        Args:
            directory_path: Path to directory to scan
            progress_callback: Optional callback for file progress (processed, total, current_file)
            block_callback: Optional callback for block progress (blocks_processed)
            
        Returns:
            BatchProcessingSummary with processing results
        """
        start_time = time.time()
        directory_path = Path(directory_path)
        
        if not directory_path.exists() or not directory_path.is_dir():
            raise ValueError(f"Directory does not exist: {directory_path}")
        
        # Find all supported files
        all_files = self._find_supported_files(directory_path)
        
        # Calculate current file hashes
        print("Calculating file hashes...")
        current_hashes = await self._calculate_file_hashes(all_files)
        
        # Determine which files need processing
        changed_files = self.cache_manager.get_changed_files(current_hashes)
        deleted_files = self.cache_manager.get_deleted_files(set(all_files))
        
        print(f"Found {len(all_files)} files, {len(changed_files)} changed, {len(deleted_files)} deleted")
        
        # Remove deleted files from vector store
        if deleted_files:
            print(f"Removing {len(deleted_files)} deleted files from index...")
            await self.vector_store.delete_by_file_paths(list(deleted_files))
            self.cache_manager.delete_multiple_hashes(deleted_files)
        
        # Process changed files
        files_to_process = [f for f in all_files if f in changed_files]
        
        if not files_to_process:
            print("No files to process")
            return BatchProcessingSummary(
                total_files=len(all_files),
                processed_files=0,
                total_blocks=0,
                indexed_blocks=0,
                errors=[],
                processing_time=time.time() - start_time
            )
        
        # Process files in batches
        print(f"Processing {len(files_to_process)} files...")
        results = await self._process_files_batch(
            files_to_process, current_hashes, progress_callback, block_callback
        )
        
        # Update cache with new hashes
        processed_hashes = {f: current_hashes[f] for f in files_to_process if f in current_hashes}
        self.cache_manager.update_multiple_hashes(processed_hashes)
        await self.cache_manager.save()
        
        processing_time = time.time() - start_time
        
        return BatchProcessingSummary(
            total_files=len(all_files),
            processed_files=len(results),
            total_blocks=sum(len(r.blocks) for r in results if r.success),
            indexed_blocks=sum(len(r.blocks) for r in results if r.success),
            errors=[r.error for r in results if not r.success and r.error],
            processing_time=processing_time
        )
    
    def _find_supported_files(self, directory_path: Path) -> List[str]:
        """Find all supported files in a directory.
        
        Args:
            directory_path: Directory to scan
            
        Returns:
            List of file paths
        """
        supported_files = []
        
        for file_path in directory_path.rglob('*'):
            if not file_path.is_file():
                continue
            
            # Check file size
            try:
                if file_path.stat().st_size > MAX_FILE_SIZE_BYTES:
                    continue
            except OSError:
                continue
            
            # Check extension
            if file_path.suffix.lower() not in SUPPORTED_EXTENSIONS:
                continue
            
            # Check ignore patterns
            relative_path = file_path.relative_to(directory_path)
            if self.ignore_spec and self.ignore_spec.match_file(str(relative_path)):
                continue
            
            supported_files.append(str(file_path))
        
        return supported_files
    
    async def _calculate_file_hashes(self, file_paths: List[str]) -> Dict[str, str]:
        """Calculate hashes for multiple files.
        
        Args:
            file_paths: List of file paths
            
        Returns:
            Dictionary mapping file paths to hashes
        """
        semaphore = asyncio.Semaphore(PARSING_CONCURRENCY)
        
        async def calculate_hash(file_path: str) -> tuple[str, str]:
            async with semaphore:
                try:
                    file_hash = await CacheManager.create_file_hash_from_path(file_path)
                    return file_path, file_hash
                except Exception:
                    return file_path, ""
        
        tasks = [calculate_hash(fp) for fp in file_paths]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        file_hashes = {}
        for result in results:
            if isinstance(result, tuple):
                file_path, file_hash = result
                if file_hash:
                    file_hashes[file_path] = file_hash
        
        return file_hashes

    async def _process_files_batch(
        self,
        file_paths: List[str],
        file_hashes: Dict[str, str],
        progress_callback: Optional[Callable[[int, int, str], None]] = None,
        block_callback: Optional[Callable[[int], None]] = None
    ) -> List[FileProcessingResult]:
        """Process a batch of files.

        Args:
            file_paths: List of file paths to process
            file_hashes: Dictionary of file hashes
            progress_callback: Optional progress callback
            block_callback: Optional block callback

        Returns:
            List of processing results
        """
        semaphore = asyncio.Semaphore(PARSING_CONCURRENCY)
        results = []
        processed_count = 0
        total_blocks = 0

        async def process_file(file_path: str) -> FileProcessingResult:
            nonlocal processed_count, total_blocks

            async with semaphore:
                start_time = time.time()

                try:
                    # Parse file
                    file_hash = file_hashes.get(file_path, "")
                    blocks = await self.parser.parse_file(file_path, file_hash=file_hash)

                    # Remove existing entries for this file
                    await self.vector_store.delete_by_file_path(file_path)

                    # Process blocks in batches
                    if blocks:
                        await self._process_blocks_batch(blocks)
                        total_blocks += len(blocks)

                        if block_callback:
                            block_callback(total_blocks)

                    processed_count += 1
                    if progress_callback:
                        progress_callback(processed_count, len(file_paths), Path(file_path).name)

                    return FileProcessingResult(
                        file_path=file_path,
                        blocks=blocks,
                        success=True,
                        processing_time=time.time() - start_time
                    )

                except Exception as e:
                    processed_count += 1
                    if progress_callback:
                        progress_callback(processed_count, len(file_paths), Path(file_path).name)

                    return FileProcessingResult(
                        file_path=file_path,
                        blocks=[],
                        success=False,
                        error=str(e),
                        processing_time=time.time() - start_time
                    )

        # Process files with progress bar
        tasks = [process_file(fp) for fp in file_paths]

        if len(file_paths) > 1:
            # Use tqdm for progress tracking
            results = []
            for coro in tqdm(asyncio.as_completed(tasks), total=len(tasks), desc="Processing files"):
                result = await coro
                results.append(result)
        else:
            results = await asyncio.gather(*tasks)

        return results

    async def _process_blocks_batch(self, blocks: List[CodeBlock]) -> None:
        """Process a batch of code blocks.

        Args:
            blocks: List of code blocks to process
        """
        if not blocks:
            return

        # Group blocks into batches for embedding
        batch_size = BATCH_SEGMENT_THRESHOLD
        for i in range(0, len(blocks), batch_size):
            batch = blocks[i:i + batch_size]
            await self._embed_and_store_batch(batch)

    async def _embed_and_store_batch(self, blocks: List[CodeBlock]) -> None:
        """Embed and store a batch of code blocks.

        Args:
            blocks: List of code blocks to embed and store
        """
        if not blocks:
            return

        # Extract text content for embedding
        texts = [block.content for block in blocks]

        # Create embeddings
        embedding_response = await self.embedder.create_embeddings(texts)
        embeddings = embedding_response.embeddings

        if len(embeddings) != len(blocks):
            raise ValueError(f"Embedding count mismatch: {len(embeddings)} != {len(blocks)}")

        # Prepare points for vector store
        points = []
        for block, embedding in zip(blocks, embeddings):
            points.append({
                "id": block.segment_hash,
                "vector": embedding,
                "metadata": {
                    "file_path": block.file_path,
                    "code_chunk": block.content,
                    "start_line": block.start_line,
                    "end_line": block.end_line,
                    "segment_hash": block.segment_hash,
                    "file_hash": block.file_hash,
                    "block_type": block.type,
                    "identifier": block.identifier or "",
                }
            })

        # Store in vector database
        await self.vector_store.upsert_points(points)

    async def process_single_file(self, file_path: str) -> FileProcessingResult:
        """Process a single file.

        Args:
            file_path: Path to the file to process

        Returns:
            FileProcessingResult
        """
        start_time = time.time()

        try:
            # Check if file is supported
            if not self.parser.is_supported_language(file_path):
                return FileProcessingResult(
                    file_path=file_path,
                    blocks=[],
                    success=False,
                    error="Unsupported file type",
                    processing_time=time.time() - start_time
                )

            # Calculate file hash
            file_hash = await CacheManager.create_file_hash_from_path(file_path)

            # Check if file has changed
            if not self.cache_manager.has_file_changed(file_path, file_hash):
                return FileProcessingResult(
                    file_path=file_path,
                    blocks=[],
                    success=True,
                    processing_time=time.time() - start_time
                )

            # Parse file
            blocks = await self.parser.parse_file(file_path, file_hash=file_hash)

            # Remove existing entries
            await self.vector_store.delete_by_file_path(file_path)

            # Process blocks
            if blocks:
                await self._process_blocks_batch(blocks)

            # Update cache
            self.cache_manager.update_hash(file_path, file_hash)
            await self.cache_manager.save()

            return FileProcessingResult(
                file_path=file_path,
                blocks=blocks,
                success=True,
                processing_time=time.time() - start_time
            )

        except Exception as e:
            return FileProcessingResult(
                file_path=file_path,
                blocks=[],
                success=False,
                error=str(e),
                processing_time=time.time() - start_time
            )

"""Main vector search engine that integrates all components."""

import asyncio
from pathlib import Path
from typing import List, Optional, Dict, Any, Callable

from .config import Config
from .models import SearchResult, BatchProcessingSummary, EmbedderProvider
from .embedders import (
    OpenAIEmbedder, 
    OllamaEmbedder, 
    OpenAICompatibleEmbedder, 
    GeminiEmbedder
)
from .parsers.tree_sitter_parser import TreeSitterParser
from .parsers.markdown_parser import MarkdownParser
from .storage.lancedb_store import LanceDBVectorStore
from .cache import CacheManager
from .scanner import FileScanner
from .search import SearchService


class VectorSearchEngine:
    """Main vector search engine that coordinates all components."""
    
    def __init__(self, config: Config):
        """Initialize the vector search engine.
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.embedder = None
        self.vector_store = None
        self.parser = None
        self.cache_manager = None
        self.scanner = None
        self.search_service = None
        self._initialized = False
    
    async def initialize(self) -> bool:
        """Initialize all components.
        
        Returns:
            True if a new collection was created, False if it already existed
        """
        if self._initialized:
            return False
        
        # Validate configuration
        self.config.validate()
        
        # Initialize embedder
        self.embedder = self._create_embedder()
        
        # Validate embedder configuration
        validation_result = await self.embedder.validate_configuration()
        if not validation_result["valid"]:
            raise RuntimeError(f"Embedder validation failed: {validation_result.get('error', 'Unknown error')}")
        
        # Initialize vector store
        self.vector_store = LanceDBVectorStore(
            db_path=self.config.lancedb_path,
            table_name=self.config.table_name,
            dimension=self.config.model_dimension
        )
        
        collection_created = await self.vector_store.initialize()
        
        # Initialize parser (combine tree-sitter and markdown parsers)
        self.parser = TreeSitterParser()
        
        # Initialize cache manager
        if self.config.cache_enabled:
            self.cache_manager = CacheManager(
                cache_path=self.config.cache_path,
                workspace_path=""  # Will be set when indexing
            )
            await self.cache_manager.initialize()
        
        # Initialize scanner
        self.scanner = FileScanner(
            parser=self.parser,
            embedder=self.embedder,
            vector_store=self.vector_store,
            cache_manager=self.cache_manager,
            ignore_patterns=self.config.ignore_patterns
        )
        
        # Initialize search service
        self.search_service = SearchService(
            embedder=self.embedder,
            vector_store=self.vector_store,
            min_score=self.config.search_min_score,
            max_results=self.config.search_max_results
        )
        
        self._initialized = True
        return collection_created
    
    def _create_embedder(self):
        """Create embedder based on configuration."""
        provider = self.config.embedder_provider
        
        if provider == EmbedderProvider.OPENAI:
            return OpenAIEmbedder(
                api_key=self.config.openai_api_key,
                model_id=self.config.model_id,
                base_url=self.config.openai_base_url,
                organization=self.config.openai_organization
            )
        
        elif provider == EmbedderProvider.OLLAMA:
            return OllamaEmbedder(
                base_url=self.config.ollama_base_url,
                model_id=self.config.ollama_model_id or self.config.model_id
            )
        
        elif provider == EmbedderProvider.OPENAI_COMPATIBLE:
            return OpenAICompatibleEmbedder(
                base_url=self.config.openai_compatible_base_url,
                api_key=self.config.openai_compatible_api_key,
                model_id=self.config.model_id
            )
        
        elif provider == EmbedderProvider.GEMINI:
            return GeminiEmbedder(
                api_key=self.config.gemini_api_key
            )
        
        else:
            raise ValueError(f"Unsupported embedder provider: {provider}")
    
    async def index_directory(
        self,
        directory_path: str,
        progress_callback: Optional[Callable[[int, int, str], None]] = None,
        block_callback: Optional[Callable[[int], None]] = None
    ) -> BatchProcessingSummary:
        """Index all supported files in a directory.
        
        Args:
            directory_path: Path to directory to index
            progress_callback: Optional callback for file progress (processed, total, current_file)
            block_callback: Optional callback for block progress (blocks_processed)
            
        Returns:
            BatchProcessingSummary with indexing results
        """
        if not self._initialized:
            await self.initialize()
        
        # Update cache manager workspace path
        if self.cache_manager:
            self.cache_manager.workspace_path = directory_path
        
        return await self.scanner.scan_directory(
            directory_path=directory_path,
            progress_callback=progress_callback,
            block_callback=block_callback
        )
    
    async def index_file(self, file_path: str) -> bool:
        """Index a single file.
        
        Args:
            file_path: Path to file to index
            
        Returns:
            True if successful, False otherwise
        """
        if not self._initialized:
            await self.initialize()
        
        result = await self.scanner.process_single_file(file_path)
        return result.success
    
    async def search(
        self,
        query: str,
        directory_prefix: Optional[str] = None,
        min_score: Optional[float] = None,
        max_results: Optional[int] = None,
        file_extensions: Optional[List[str]] = None
    ) -> List[SearchResult]:
        """Perform semantic search on indexed code.
        
        Args:
            query: Search query text
            directory_prefix: Optional directory prefix to filter results
            min_score: Optional minimum score threshold
            max_results: Optional maximum number of results
            file_extensions: Optional list of file extensions to filter by
            
        Returns:
            List of search results sorted by relevance score
        """
        if not self._initialized:
            await self.initialize()
        
        return await self.search_service.search(
            query=query,
            directory_prefix=directory_prefix,
            min_score=min_score,
            max_results=max_results,
            file_extensions=file_extensions
        )
    
    async def search_similar_code(
        self,
        code_snippet: str,
        min_score: Optional[float] = None,
        max_results: Optional[int] = None,
        exclude_file: Optional[str] = None
    ) -> List[SearchResult]:
        """Search for code similar to the provided snippet.
        
        Args:
            code_snippet: Code snippet to find similar code for
            min_score: Optional minimum score threshold
            max_results: Optional maximum number of results
            exclude_file: Optional file path to exclude from results
            
        Returns:
            List of similar code results
        """
        if not self._initialized:
            await self.initialize()
        
        return await self.search_service.search_similar_code(
            code_snippet=code_snippet,
            min_score=min_score,
            max_results=max_results,
            exclude_file=exclude_file
        )
    
    async def search_by_function_name(
        self,
        function_name: str,
        min_score: Optional[float] = None,
        max_results: Optional[int] = None
    ) -> List[SearchResult]:
        """Search for functions by name.
        
        Args:
            function_name: Function name to search for
            min_score: Optional minimum score threshold
            max_results: Optional maximum number of results
            
        Returns:
            List of function search results
        """
        if not self._initialized:
            await self.initialize()
        
        return await self.search_service.search_by_function_name(
            function_name=function_name,
            min_score=min_score,
            max_results=max_results
        )
    
    async def search_by_class_name(
        self,
        class_name: str,
        min_score: Optional[float] = None,
        max_results: Optional[int] = None
    ) -> List[SearchResult]:
        """Search for classes by name.
        
        Args:
            class_name: Class name to search for
            min_score: Optional minimum score threshold
            max_results: Optional maximum number of results
            
        Returns:
            List of class search results
        """
        if not self._initialized:
            await self.initialize()
        
        return await self.search_service.search_by_class_name(
            class_name=class_name,
            min_score=min_score,
            max_results=max_results
        )
    
    async def clear_index(self) -> None:
        """Clear all indexed data."""
        if not self._initialized:
            await self.initialize()
        
        await self.vector_store.clear_collection()
        
        if self.cache_manager:
            await self.cache_manager.clear()
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the indexed collection.
        
        Returns:
            Dictionary with collection statistics
        """
        if not self._initialized:
            await self.initialize()
        
        return await self.search_service.get_collection_stats()
    
    async def close(self) -> None:
        """Close the engine and clean up resources."""
        if self.cache_manager:
            await self.cache_manager.save()
        
        self._initialized = False

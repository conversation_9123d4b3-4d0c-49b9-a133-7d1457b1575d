"""Gemini embedder implementation."""

from typing import List, Optional, Dict, Any

from .openai_compatible_embedder import Open<PERSON><PERSON>om<PERSON>tible<PERSON>mbedder
from ..models import EmbeddingResponse
from ..constants import GEMINI_MAX_ITEM_TOKENS


class GeminiEmbedder(OpenAICompatibleEmbedder):
    """Gemini embedder implementation using OpenAI-compatible interface."""
    
    GEMINI_BASE_URL = "https://generativelanguage.googleapis.com/v1beta/openai/"
    GEMINI_MODEL = "text-embedding-004"
    GEMINI_DIMENSION = 768
    
    def __init__(self, api_key: str):
        """Initialize Gemini embedder.
        
        Args:
            api_key: Gemini API key
        """
        if not api_key:
            raise ValueError("Gemini API key is required")
        
        super().__init__(
            base_url=self.GEMINI_BASE_URL,
            api_key=api_key,
            model_id=self.GEMINI_MODEL,
            max_tokens=GEMINI_MAX_ITEM_TOKENS
        )
    
    async def create_embeddings(
        self, 
        texts: List[str], 
        model: Optional[str] = None
    ) -> EmbeddingResponse:
        """Create embeddings using Gemini API.
        
        Args:
            texts: List of texts to embed
            model: Optional model override (ignored - always uses Gemini model)
            
        Returns:
            EmbeddingResponse with embeddings and usage
        """
        # Always use the fixed Gemini model
        return await super().create_embeddings(texts, self.GEMINI_MODEL)
    
    async def validate_configuration(self) -> Dict[str, Any]:
        """Validate Gemini configuration.
        
        Returns:
            Validation result with success status and optional error
        """
        try:
            # Test with minimal request
            result = await self.create_embeddings(["test"])
            
            if not result.embeddings or len(result.embeddings) == 0:
                return {
                    "valid": False,
                    "error": "Invalid response from Gemini API"
                }
            
            return {"valid": True}
            
        except Exception as error:
            error_msg = str(error)
            
            if "401" in error_msg or "unauthorized" in error_msg.lower():
                return {
                    "valid": False,
                    "error": "Invalid Gemini API key"
                }
            elif "403" in error_msg or "forbidden" in error_msg.lower():
                return {
                    "valid": False,
                    "error": "Gemini API access forbidden - check API key permissions"
                }
            elif "quota" in error_msg.lower() or "limit" in error_msg.lower():
                return {
                    "valid": False,
                    "error": "Gemini API quota exceeded"
                }
            else:
                return {
                    "valid": False,
                    "error": f"Gemini validation failed: {error_msg}"
                }
    
    @property
    def embedder_info(self) -> Dict[str, str]:
        """Get embedder information.
        
        Returns:
            Dictionary with embedder info
        """
        return {
            "name": "gemini",
            "model": self.GEMINI_MODEL,
            "dimension": str(self.GEMINI_DIMENSION)
        }
    
    @classmethod
    def get_dimension(cls) -> int:
        """Get the fixed dimension for Gemini embeddings.
        
        Returns:
            Embedding dimension
        """
        return cls.GEMINI_DIMENSION

"""OpenAI-compatible embedder implementation."""

import asyncio
import base64
import struct
import aiohttp
from typing import List, Optional, Dict, Any
from openai import AsyncOpenAI

from .base import BaseEmbedder
from ..models import EmbeddingResponse
from ..constants import MAX_BATCH_TOKENS, MAX_ITEM_TOKENS, MAX_BATCH_RETRIES, INITIAL_RETRY_DELAY_MS


class OpenAICompatibleEmbedder(BaseEmbedder):
    """OpenAI-compatible embedder implementation."""
    
    def __init__(
        self,
        base_url: str,
        api_key: str,
        model_id: str,
        max_tokens: int = MAX_ITEM_TOKENS
    ):
        """Initialize OpenAI-compatible embedder.
        
        Args:
            base_url: API base URL
            api_key: API key for authentication
            model_id: Model to use for embeddings
            max_tokens: Maximum tokens per input
        """
        super().__init__(model_id, max_tokens)
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.is_full_url = self._is_full_endpoint_url(base_url)
        
        if not self.is_full_url:
            # Use OpenAI SDK for base URLs
            self.client = AsyncOpenAI(
                api_key=api_key,
                base_url=base_url
            )
    
    def _is_full_endpoint_url(self, url: str) -> bool:
        """Check if URL is a full endpoint URL."""
        return "/embeddings" in url or "/v1/embeddings" in url
    
    async def create_embeddings(
        self, 
        texts: List[str], 
        model: Optional[str] = None
    ) -> EmbeddingResponse:
        """Create embeddings using OpenAI-compatible API.
        
        Args:
            texts: List of texts to embed
            model: Optional model override
            
        Returns:
            EmbeddingResponse with embeddings and usage
        """
        model_to_use = model or self.model_id
        
        # Chunk texts by token limits
        text_batches = self.chunk_texts_by_tokens(texts, MAX_BATCH_TOKENS)
        
        all_embeddings = []
        total_prompt_tokens = 0
        total_tokens = 0
        
        for batch in text_batches:
            batch_result = await self._embed_batch_with_retries(batch, model_to_use)
            all_embeddings.extend(batch_result["embeddings"])
            total_prompt_tokens += batch_result["usage"]["prompt_tokens"]
            total_tokens += batch_result["usage"]["total_tokens"]
        
        return EmbeddingResponse(
            embeddings=all_embeddings,
            usage={
                "prompt_tokens": total_prompt_tokens,
                "total_tokens": total_tokens
            }
        )
    
    async def _embed_batch_with_retries(
        self, 
        batch_texts: List[str], 
        model: str
    ) -> Dict[str, Any]:
        """Embed a batch of texts with retry logic.
        
        Args:
            batch_texts: Texts to embed
            model: Model to use
            
        Returns:
            Dictionary with embeddings and usage
        """
        for attempt in range(MAX_BATCH_RETRIES):
            try:
                if self.is_full_url:
                    # Use direct HTTP request for full endpoint URLs
                    response = await self._make_direct_request(batch_texts, model)
                else:
                    # Use OpenAI SDK for base URLs
                    response = await self.client.embeddings.create(
                        input=batch_texts,
                        model=model,
                        encoding_format="base64"  # Handle large dimensions properly
                    )
                
                # Process base64 embeddings if needed
                processed_embeddings = []
                for item in response.data:
                    if isinstance(item.embedding, str):
                        # Decode base64 embedding
                        embedding_bytes = base64.b64decode(item.embedding)
                        float_count = len(embedding_bytes) // 4
                        embedding = list(struct.unpack(f'{float_count}f', embedding_bytes))
                        processed_embeddings.append(embedding)
                    else:
                        processed_embeddings.append(item.embedding)
                
                return {
                    "embeddings": processed_embeddings,
                    "usage": {
                        "prompt_tokens": response.usage.prompt_tokens if response.usage else 0,
                        "total_tokens": response.usage.total_tokens if response.usage else 0
                    }
                }
                
            except Exception as error:
                has_more_attempts = attempt < MAX_BATCH_RETRIES - 1
                
                # Check for rate limit error
                if hasattr(error, 'status_code') and error.status_code == 429 and has_more_attempts:
                    delay_ms = INITIAL_RETRY_DELAY_MS * (2 ** attempt)
                    await asyncio.sleep(delay_ms / 1000)
                    continue
                
                # Re-raise error if no more attempts or not a rate limit error
                raise error
        
        raise Exception(f"Failed to create embeddings after {MAX_BATCH_RETRIES} attempts")
    
    async def _make_direct_request(self, texts: List[str], model: str) -> Any:
        """Make direct HTTP request to full endpoint URL.
        
        Args:
            texts: Texts to embed
            model: Model to use
            
        Returns:
            Response object
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "input": texts,
            "model": model,
            "encoding_format": "base64"
        }
        
        timeout = aiohttp.ClientTimeout(total=60)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(self.base_url, json=payload, headers=headers) as response:
                if not response.ok:
                    error_text = await response.text()
                    raise Exception(f"API request failed: {response.status} {error_text}")
                
                return await response.json()
    
    async def validate_configuration(self) -> Dict[str, Any]:
        """Validate OpenAI-compatible configuration.
        
        Returns:
            Validation result with success status and optional error
        """
        try:
            # Test with minimal request
            result = await self.create_embeddings(["test"])
            
            if not result.embeddings or len(result.embeddings) == 0:
                return {
                    "valid": False,
                    "error": "Invalid response from OpenAI-compatible API"
                }
            
            return {"valid": True}
            
        except Exception as error:
            return {
                "valid": False,
                "error": f"OpenAI-compatible validation failed: {str(error)}"
            }
    
    @property
    def embedder_info(self) -> Dict[str, str]:
        """Get embedder information.
        
        Returns:
            Dictionary with embedder info
        """
        return {
            "name": "openai_compatible",
            "model": self.model_id,
            "base_url": self.base_url
        }

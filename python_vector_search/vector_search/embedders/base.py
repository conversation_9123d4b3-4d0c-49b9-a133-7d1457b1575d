"""Base embedder interface."""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any

from ..models import EmbeddingResponse


class BaseEmbedder(ABC):
    """Abstract base class for all embedders."""
    
    def __init__(self, model_id: str, max_tokens: int = 8191):
        """Initialize the embedder.
        
        Args:
            model_id: The model identifier to use
            max_tokens: Maximum tokens per text input
        """
        self.model_id = model_id
        self.max_tokens = max_tokens
    
    @abstractmethod
    async def create_embeddings(
        self, 
        texts: List[str], 
        model: Optional[str] = None
    ) -> EmbeddingResponse:
        """Create embeddings for the given texts.
        
        Args:
            texts: List of text strings to embed
            model: Optional model override
            
        Returns:
            EmbeddingResponse containing embeddings and usage info
        """
        pass
    
    @abstractmethod
    async def validate_configuration(self) -> Dict[str, Any]:
        """Validate the embedder configuration.
        
        Returns:
            Dictionary with 'valid' boolean and optional 'error' message
        """
        pass
    
    @property
    @abstractmethod
    def embedder_info(self) -> Dict[str, str]:
        """Get information about this embedder.
        
        Returns:
            Dictionary with embedder information
        """
        pass
    
    def estimate_tokens(self, text: str) -> int:
        """Estimate the number of tokens in a text.
        
        Args:
            text: Input text
            
        Returns:
            Estimated token count
        """
        # Simple estimation: ~4 characters per token
        return len(text) // 4
    
    def chunk_texts_by_tokens(self, texts: List[str], max_batch_tokens: int) -> List[List[str]]:
        """Chunk texts into batches based on token limits.
        
        Args:
            texts: List of texts to chunk
            max_batch_tokens: Maximum tokens per batch
            
        Returns:
            List of text batches
        """
        batches = []
        current_batch = []
        current_tokens = 0
        
        for text in texts:
            text_tokens = self.estimate_tokens(text)
            
            # Skip texts that are too long
            if text_tokens > self.max_tokens:
                continue
                
            # Start new batch if adding this text would exceed limit
            if current_tokens + text_tokens > max_batch_tokens and current_batch:
                batches.append(current_batch)
                current_batch = [text]
                current_tokens = text_tokens
            else:
                current_batch.append(text)
                current_tokens += text_tokens
        
        # Add final batch if not empty
        if current_batch:
            batches.append(current_batch)
            
        return batches

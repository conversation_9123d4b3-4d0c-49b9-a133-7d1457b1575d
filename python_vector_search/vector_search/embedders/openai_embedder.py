"""OpenAI embedder implementation."""

import async<PERSON>
from typing import List, Optional, Dict, Any
import openai
from openai import Async<PERSON>penA<PERSON>

from .base import BaseEmbedder
from ..models import EmbeddingResponse
from ..constants import MAX_BATCH_TOKENS, MAX_ITEM_TOKENS, MAX_BATCH_RETRIES, INITIAL_RETRY_DELAY_MS


class OpenAIEmbedder(BaseEmbedder):
    """OpenAI embedder implementation."""
    
    def __init__(
        self, 
        api_key: str,
        model_id: str = "text-embedding-3-small",
        base_url: Optional[str] = None,
        organization: Optional[str] = None,
        max_tokens: int = MAX_ITEM_TOKENS
    ):
        """Initialize OpenAI embedder.
        
        Args:
            api_key: OpenAI API key
            model_id: Model to use for embeddings
            base_url: Optional base URL override
            organization: Optional organization ID
            max_tokens: Maximum tokens per input
        """
        super().__init__(model_id, max_tokens)
        
        self.client = AsyncOpenAI(
            api_key=api_key,
            base_url=base_url,
            organization=organization
        )
    
    async def create_embeddings(
        self, 
        texts: List[str], 
        model: Optional[str] = None
    ) -> EmbeddingResponse:
        """Create embeddings using OpenAI API.
        
        Args:
            texts: List of texts to embed
            model: Optional model override
            
        Returns:
            EmbeddingResponse with embeddings and usage
        """
        model_to_use = model or self.model_id
        
        # Chunk texts by token limits
        text_batches = self.chunk_texts_by_tokens(texts, MAX_BATCH_TOKENS)
        
        all_embeddings = []
        total_prompt_tokens = 0
        total_tokens = 0
        
        for batch in text_batches:
            batch_result = await self._embed_batch_with_retries(batch, model_to_use)
            all_embeddings.extend(batch_result["embeddings"])
            total_prompt_tokens += batch_result["usage"]["prompt_tokens"]
            total_tokens += batch_result["usage"]["total_tokens"]
        
        return EmbeddingResponse(
            embeddings=all_embeddings,
            usage={
                "prompt_tokens": total_prompt_tokens,
                "total_tokens": total_tokens
            }
        )
    
    async def _embed_batch_with_retries(
        self, 
        batch_texts: List[str], 
        model: str
    ) -> Dict[str, Any]:
        """Embed a batch of texts with retry logic.
        
        Args:
            batch_texts: Texts to embed
            model: Model to use
            
        Returns:
            Dictionary with embeddings and usage
        """
        for attempt in range(MAX_BATCH_RETRIES):
            try:
                response = await self.client.embeddings.create(
                    input=batch_texts,
                    model=model
                )
                
                return {
                    "embeddings": [item.embedding for item in response.data],
                    "usage": {
                        "prompt_tokens": response.usage.prompt_tokens if response.usage else 0,
                        "total_tokens": response.usage.total_tokens if response.usage else 0
                    }
                }
                
            except Exception as error:
                has_more_attempts = attempt < MAX_BATCH_RETRIES - 1
                
                # Check for rate limit error
                if hasattr(error, 'status_code') and error.status_code == 429 and has_more_attempts:
                    delay_ms = INITIAL_RETRY_DELAY_MS * (2 ** attempt)
                    await asyncio.sleep(delay_ms / 1000)
                    continue
                
                # Re-raise error if no more attempts or not a rate limit error
                raise error
        
        raise Exception(f"Failed to create embeddings after {MAX_BATCH_RETRIES} attempts")
    
    async def validate_configuration(self) -> Dict[str, Any]:
        """Validate OpenAI configuration.
        
        Returns:
            Validation result with success status and optional error
        """
        try:
            # Test with minimal request
            response = await self.client.embeddings.create(
                input=["test"],
                model=self.model_id
            )
            
            if not response.data or len(response.data) == 0:
                return {
                    "valid": False,
                    "error": "Invalid response format from OpenAI API"
                }
            
            return {"valid": True}
            
        except Exception as error:
            return {
                "valid": False,
                "error": f"OpenAI validation failed: {str(error)}"
            }
    
    @property
    def embedder_info(self) -> Dict[str, str]:
        """Get embedder information.
        
        Returns:
            Dictionary with embedder info
        """
        return {
            "name": "openai",
            "model": self.model_id
        }

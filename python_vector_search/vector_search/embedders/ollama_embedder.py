"""Ollama embedder implementation."""

import asyncio
import aiohttp
from typing import List, Optional, Dict, Any

from .base import <PERSON>Embedder
from ..models import EmbeddingResponse
from ..constants import MAX_ITEM_TOKENS


class OllamaEmbedder(BaseEmbedder):
    """Ollama embedder implementation."""
    
    def __init__(
        self,
        base_url: str = "http://localhost:11434",
        model_id: str = "nomic-embed-text:latest",
        max_tokens: int = MAX_ITEM_TOKENS
    ):
        """Initialize Ollama embedder.
        
        Args:
            base_url: Ollama server base URL
            model_id: Model to use for embeddings
            max_tokens: Maximum tokens per input
        """
        super().__init__(model_id, max_tokens)
        self.base_url = base_url.rstrip('/')
    
    async def create_embeddings(
        self, 
        texts: List[str], 
        model: Optional[str] = None
    ) -> EmbeddingResponse:
        """Create embeddings using Ollama API.
        
        Args:
            texts: List of texts to embed
            model: Optional model override
            
        Returns:
            EmbeddingResponse with embeddings
        """
        model_to_use = model or self.model_id
        url = f"{self.base_url}/api/embed"
        
        # Filter texts that are too long
        valid_texts = [
            text for text in texts 
            if self.estimate_tokens(text) <= self.max_tokens
        ]
        
        if not valid_texts:
            return EmbeddingResponse(embeddings=[])
        
        try:
            timeout = aiohttp.ClientTimeout(total=30)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(
                    url,
                    json={
                        "model": model_to_use,
                        "input": valid_texts
                    }
                ) as response:
                    if not response.ok:
                        error_text = await response.text()
                        raise Exception(
                            f"Ollama request failed: {response.status} {response.reason}. "
                            f"Error: {error_text}"
                        )
                    
                    data = await response.json()
                    embeddings = data.get("embeddings", [])
                    
                    if not embeddings or not isinstance(embeddings, list):
                        raise Exception("Invalid response structure from Ollama API")
                    
                    return EmbeddingResponse(embeddings=embeddings)
                    
        except asyncio.TimeoutError:
            raise Exception("Ollama request timed out")
        except aiohttp.ClientError as error:
            if "Connection refused" in str(error):
                raise Exception(f"Ollama service not running at {self.base_url}")
            elif "Name or service not known" in str(error):
                raise Exception(f"Ollama host not found: {self.base_url}")
            else:
                raise Exception(f"Ollama connection failed: {str(error)}")
        except Exception as error:
            raise Exception(f"Ollama embedding failed: {str(error)}")
    
    async def validate_configuration(self) -> Dict[str, Any]:
        """Validate Ollama configuration.
        
        Returns:
            Validation result with success status and optional error
        """
        try:
            # Test with minimal request
            result = await self.create_embeddings(["test"])
            
            if not result.embeddings or len(result.embeddings) == 0:
                return {
                    "valid": False,
                    "error": "Invalid response from Ollama API"
                }
            
            return {"valid": True}
            
        except Exception as error:
            error_msg = str(error)
            
            if "Connection refused" in error_msg or "service not running" in error_msg:
                return {
                    "valid": False,
                    "error": f"Ollama service not running at {self.base_url}"
                }
            elif "Name or service not known" in error_msg or "host not found" in error_msg:
                return {
                    "valid": False,
                    "error": f"Ollama host not found: {self.base_url}"
                }
            elif "timed out" in error_msg:
                return {
                    "valid": False,
                    "error": "Connection to Ollama timed out"
                }
            else:
                return {
                    "valid": False,
                    "error": f"Ollama validation failed: {error_msg}"
                }
    
    @property
    def embedder_info(self) -> Dict[str, str]:
        """Get embedder information.
        
        Returns:
            Dictionary with embedder info
        """
        return {
            "name": "ollama",
            "model": self.model_id,
            "base_url": self.base_url
        }

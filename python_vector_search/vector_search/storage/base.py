"""Base vector store interface."""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any

from ..models import SearchResult


class BaseVectorStore(ABC):
    """Abstract base class for vector stores."""
    
    def __init__(self, dimension: int):
        """Initialize the vector store.
        
        Args:
            dimension: Vector dimension
        """
        self.dimension = dimension
    
    @abstractmethod
    async def initialize(self) -> bool:
        """Initialize the vector store.
        
        Returns:
            True if a new collection was created, False if it already existed
        """
        pass
    
    @abstractmethod
    async def upsert_points(self, points: List[Dict[str, Any]]) -> None:
        """Upsert points into the vector store.
        
        Args:
            points: List of points to upsert, each containing:
                - id: Unique identifier
                - vector: Embedding vector
                - metadata: Additional metadata
        """
        pass
    
    @abstractmethod
    async def search(
        self,
        query_vector: List[float],
        directory_prefix: Optional[str] = None,
        min_score: Optional[float] = None,
        max_results: Optional[int] = None
    ) -> List[SearchResult]:
        """Search for similar vectors.
        
        Args:
            query_vector: Query vector
            directory_prefix: Optional directory prefix filter
            min_score: Optional minimum score threshold
            max_results: Optional maximum number of results
            
        Returns:
            List of search results
        """
        pass
    
    @abstractmethod
    async def delete_by_file_path(self, file_path: str) -> None:
        """Delete all points for a specific file path.
        
        Args:
            file_path: File path to delete points for
        """
        pass
    
    @abstractmethod
    async def delete_by_file_paths(self, file_paths: List[str]) -> None:
        """Delete points for multiple file paths.
        
        Args:
            file_paths: List of file paths to delete points for
        """
        pass
    
    @abstractmethod
    async def clear_collection(self) -> None:
        """Clear all points from the collection."""
        pass
    
    @abstractmethod
    async def delete_collection(self) -> None:
        """Delete the entire collection."""
        pass
    
    @abstractmethod
    async def collection_exists(self) -> bool:
        """Check if the collection exists.
        
        Returns:
            True if collection exists, False otherwise
        """
        pass
    
    @abstractmethod
    async def get_collection_info(self) -> Dict[str, Any]:
        """Get information about the collection.
        
        Returns:
            Dictionary with collection information
        """
        pass

"""LanceDB vector store implementation."""

import os
import asyncio
from pathlib import Path
from typing import List, Optional, Dict, Any
import lancedb
import pandas as pd
import pyarrow as pa

from .base import BaseVectorStore
from ..models import SearchResult
from ..constants import DEFAULT_SEARCH_MIN_SCORE, DEFAULT_MAX_SEARCH_RESULTS


class LanceDBVectorStore(BaseVectorStore):
    """LanceDB implementation of the vector store interface."""
    
    def __init__(
        self,
        db_path: str,
        table_name: str = "code_vectors",
        dimension: int = 1536
    ):
        """Initialize LanceDB vector store.
        
        Args:
            db_path: Path to the LanceDB database
            table_name: Name of the table to use
            dimension: Vector dimension
        """
        super().__init__(dimension)
        self.db_path = Path(db_path)
        self.table_name = table_name
        self.db = None
        self.table = None
        
        # Define schema
        self.schema = pa.schema([
            pa.field("id", pa.string()),
            pa.field("vector", pa.list_(pa.float32(), self.dimension)),
            pa.field("file_path", pa.string()),
            pa.field("code_chunk", pa.string()),
            pa.field("start_line", pa.int32()),
            pa.field("end_line", pa.int32()),
            pa.field("segment_hash", pa.string()),
            pa.field("file_hash", pa.string()),
            pa.field("block_type", pa.string()),
            pa.field("identifier", pa.string()),
            pa.field("path_segments", pa.list_(pa.string())),
        ])
    
    async def initialize(self) -> bool:
        """Initialize the LanceDB connection and table.
        
        Returns:
            True if a new table was created, False if it already existed
        """
        # Create database directory if it doesn't exist
        self.db_path.mkdir(parents=True, exist_ok=True)
        
        # Connect to database
        self.db = lancedb.connect(str(self.db_path))
        
        # Check if table exists
        table_exists = self.table_name in self.db.table_names()
        
        if not table_exists:
            # Create new table with schema
            empty_data = pd.DataFrame({
                "id": pd.Series([], dtype="string"),
                "vector": pd.Series([], dtype="object"),
                "file_path": pd.Series([], dtype="string"),
                "code_chunk": pd.Series([], dtype="string"),
                "start_line": pd.Series([], dtype="int32"),
                "end_line": pd.Series([], dtype="int32"),
                "segment_hash": pd.Series([], dtype="string"),
                "file_hash": pd.Series([], dtype="string"),
                "block_type": pd.Series([], dtype="string"),
                "identifier": pd.Series([], dtype="string"),
                "path_segments": pd.Series([], dtype="object"),
            })
            
            self.table = self.db.create_table(self.table_name, empty_data, schema=self.schema)
            return True
        else:
            # Open existing table
            self.table = self.db.open_table(self.table_name)
            return False
    
    async def upsert_points(self, points: List[Dict[str, Any]]) -> None:
        """Upsert points into the LanceDB table.
        
        Args:
            points: List of points to upsert
        """
        if not self.table:
            raise RuntimeError("Vector store not initialized")
        
        if not points:
            return
        
        # Convert points to DataFrame
        data = []
        for point in points:
            metadata = point.get("metadata", {})
            
            # Extract path segments from file path
            file_path = metadata.get("file_path", "")
            path_segments = file_path.split(os.sep) if file_path else []
            
            data.append({
                "id": point["id"],
                "vector": point["vector"],
                "file_path": file_path,
                "code_chunk": metadata.get("code_chunk", ""),
                "start_line": metadata.get("start_line", 0),
                "end_line": metadata.get("end_line", 0),
                "segment_hash": metadata.get("segment_hash", ""),
                "file_hash": metadata.get("file_hash", ""),
                "block_type": metadata.get("block_type", ""),
                "identifier": metadata.get("identifier", ""),
                "path_segments": path_segments,
            })
        
        df = pd.DataFrame(data)
        
        # Add data to table (LanceDB handles upserts automatically)
        self.table.add(df)
    
    async def search(
        self,
        query_vector: List[float],
        directory_prefix: Optional[str] = None,
        min_score: Optional[float] = None,
        max_results: Optional[int] = None
    ) -> List[SearchResult]:
        """Search for similar vectors.
        
        Args:
            query_vector: Query vector
            directory_prefix: Optional directory prefix filter
            min_score: Optional minimum score threshold
            max_results: Optional maximum number of results
            
        Returns:
            List of search results
        """
        if not self.table:
            raise RuntimeError("Vector store not initialized")
        
        # Set defaults
        min_score = min_score or DEFAULT_SEARCH_MIN_SCORE
        max_results = max_results or DEFAULT_MAX_SEARCH_RESULTS
        
        # Perform vector search
        search_query = self.table.search(query_vector).limit(max_results)
        
        # Add directory prefix filter if specified
        if directory_prefix:
            # Normalize directory prefix
            prefix_segments = directory_prefix.split(os.sep)
            prefix_segments = [seg for seg in prefix_segments if seg]  # Remove empty segments
            
            # Create filter condition for path segments
            if prefix_segments:
                # For LanceDB, we need to filter after search since it doesn't support complex filters
                # This is less efficient but works for the current implementation
                pass
        
        # Execute search
        results = search_query.to_pandas()
        
        # Convert to SearchResult objects
        search_results = []
        for _, row in results.iterrows():
            # Calculate similarity score (LanceDB returns distance, convert to similarity)
            # Assuming cosine distance, similarity = 1 - distance
            score = 1.0 - row.get("_distance", 0.0)
            
            # Apply score threshold
            if score < min_score:
                continue
            
            # Apply directory prefix filter if specified
            if directory_prefix and prefix_segments:
                file_path_segments = row.get("path_segments", [])
                if not self._matches_prefix(file_path_segments, prefix_segments):
                    continue
            
            search_results.append(SearchResult(
                id=str(row["id"]),
                score=score,
                file_path=row["file_path"],
                code_chunk=row["code_chunk"],
                start_line=int(row["start_line"]),
                end_line=int(row["end_line"]),
                metadata={
                    "segment_hash": row["segment_hash"],
                    "file_hash": row["file_hash"],
                    "block_type": row["block_type"],
                    "identifier": row["identifier"],
                }
            ))
        
        return search_results
    
    def _matches_prefix(self, file_path_segments: List[str], prefix_segments: List[str]) -> bool:
        """Check if file path segments match the prefix.
        
        Args:
            file_path_segments: File path segments
            prefix_segments: Prefix segments to match
            
        Returns:
            True if matches, False otherwise
        """
        if len(prefix_segments) > len(file_path_segments):
            return False
        
        for i, prefix_seg in enumerate(prefix_segments):
            if i >= len(file_path_segments) or file_path_segments[i] != prefix_seg:
                return False
        
        return True

    async def delete_by_file_path(self, file_path: str) -> None:
        """Delete all points for a specific file path.

        Args:
            file_path: File path to delete points for
        """
        if not self.table:
            raise RuntimeError("Vector store not initialized")

        # Delete rows matching the file path
        self.table.delete(f"file_path = '{file_path}'")

    async def delete_by_file_paths(self, file_paths: List[str]) -> None:
        """Delete points for multiple file paths.

        Args:
            file_paths: List of file paths to delete points for
        """
        if not self.table:
            raise RuntimeError("Vector store not initialized")

        if not file_paths:
            return

        # Create filter condition for multiple file paths
        file_path_conditions = [f"file_path = '{fp}'" for fp in file_paths]
        condition = " OR ".join(file_path_conditions)

        self.table.delete(condition)

    async def clear_collection(self) -> None:
        """Clear all points from the collection."""
        if not self.table:
            raise RuntimeError("Vector store not initialized")

        # Delete all rows
        self.table.delete("true")

    async def delete_collection(self) -> None:
        """Delete the entire collection."""
        if not self.db:
            raise RuntimeError("Vector store not initialized")

        # Drop the table
        self.db.drop_table(self.table_name)
        self.table = None

    async def collection_exists(self) -> bool:
        """Check if the collection exists.

        Returns:
            True if collection exists, False otherwise
        """
        if not self.db:
            return False

        return self.table_name in self.db.table_names()

    async def get_collection_info(self) -> Dict[str, Any]:
        """Get information about the collection.

        Returns:
            Dictionary with collection information
        """
        if not self.table:
            raise RuntimeError("Vector store not initialized")

        # Get table statistics
        try:
            count = self.table.count_rows()
            schema_info = {
                "fields": [field.name for field in self.table.schema],
                "dimension": self.dimension
            }

            return {
                "name": self.table_name,
                "count": count,
                "schema": schema_info,
                "db_path": str(self.db_path)
            }
        except Exception as e:
            return {
                "name": self.table_name,
                "error": str(e),
                "db_path": str(self.db_path)
            }

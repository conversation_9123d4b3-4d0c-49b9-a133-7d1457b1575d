"""Cache management for file hashes and processing state."""

import json
import asyncio
from pathlib import Path
from typing import Dict, Optional, Set
import aiofiles
import hashlib


class CacheManager:
    """Manages file hash cache to avoid reprocessing unchanged files."""
    
    def __init__(self, cache_path: str, workspace_path: str):
        """Initialize cache manager.
        
        Args:
            cache_path: Path to the cache file
            workspace_path: Path to the workspace being indexed
        """
        self.cache_path = Path(cache_path)
        self.workspace_path = workspace_path
        self.file_hashes: Dict[str, str] = {}
        self._save_lock = asyncio.Lock()
        self._dirty = False
        
        # Create cache directory if it doesn't exist
        self.cache_path.parent.mkdir(parents=True, exist_ok=True)
    
    async def initialize(self) -> None:
        """Initialize the cache by loading existing data."""
        try:
            if self.cache_path.exists():
                async with aiofiles.open(self.cache_path, 'r', encoding='utf-8') as f:
                    content = await f.read()
                    self.file_hashes = json.loads(content)
            else:
                self.file_hashes = {}
        except Exception as e:
            print(f"Warning: Failed to load cache from {self.cache_path}: {e}")
            self.file_hashes = {}
    
    async def save(self) -> None:
        """Save the cache to disk."""
        if not self._dirty:
            return
            
        async with self._save_lock:
            try:
                # Write to temporary file first, then rename for atomicity
                temp_path = self.cache_path.with_suffix('.tmp')
                async with aiofiles.open(temp_path, 'w', encoding='utf-8') as f:
                    await f.write(json.dumps(self.file_hashes, indent=2))
                
                # Atomic rename
                temp_path.replace(self.cache_path)
                self._dirty = False
                
            except Exception as e:
                print(f"Warning: Failed to save cache to {self.cache_path}: {e}")
    
    async def clear(self) -> None:
        """Clear the cache file and in-memory data."""
        self.file_hashes = {}
        self._dirty = True
        
        try:
            if self.cache_path.exists():
                self.cache_path.unlink()
        except Exception as e:
            print(f"Warning: Failed to delete cache file {self.cache_path}: {e}")
    
    def get_hash(self, file_path: str) -> Optional[str]:
        """Get the cached hash for a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Cached hash or None if not found
        """
        return self.file_hashes.get(file_path)
    
    def update_hash(self, file_path: str, file_hash: str) -> None:
        """Update the hash for a file.
        
        Args:
            file_path: Path to the file
            file_hash: New hash value
        """
        if self.file_hashes.get(file_path) != file_hash:
            self.file_hashes[file_path] = file_hash
            self._dirty = True
    
    def delete_hash(self, file_path: str) -> None:
        """Delete the hash for a file.
        
        Args:
            file_path: Path to the file
        """
        if file_path in self.file_hashes:
            del self.file_hashes[file_path]
            self._dirty = True
    
    def get_all_hashes(self) -> Dict[str, str]:
        """Get a copy of all file hashes.
        
        Returns:
            Dictionary of file path to hash mappings
        """
        return self.file_hashes.copy()
    
    def has_file_changed(self, file_path: str, current_hash: str) -> bool:
        """Check if a file has changed since last processing.
        
        Args:
            file_path: Path to the file
            current_hash: Current hash of the file
            
        Returns:
            True if file has changed or is new, False if unchanged
        """
        cached_hash = self.get_hash(file_path)
        return cached_hash != current_hash
    
    def get_changed_files(self, file_hashes: Dict[str, str]) -> Set[str]:
        """Get set of files that have changed.
        
        Args:
            file_hashes: Dictionary of current file hashes
            
        Returns:
            Set of file paths that have changed
        """
        changed_files = set()
        
        for file_path, current_hash in file_hashes.items():
            if self.has_file_changed(file_path, current_hash):
                changed_files.add(file_path)
        
        return changed_files
    
    def get_deleted_files(self, current_files: Set[str]) -> Set[str]:
        """Get set of files that have been deleted.
        
        Args:
            current_files: Set of currently existing file paths
            
        Returns:
            Set of file paths that have been deleted
        """
        cached_files = set(self.file_hashes.keys())
        return cached_files - current_files
    
    def update_multiple_hashes(self, file_hashes: Dict[str, str]) -> None:
        """Update hashes for multiple files.
        
        Args:
            file_hashes: Dictionary of file path to hash mappings
        """
        changed = False
        for file_path, file_hash in file_hashes.items():
            if self.file_hashes.get(file_path) != file_hash:
                self.file_hashes[file_path] = file_hash
                changed = True
        
        if changed:
            self._dirty = True
    
    def delete_multiple_hashes(self, file_paths: Set[str]) -> None:
        """Delete hashes for multiple files.
        
        Args:
            file_paths: Set of file paths to delete
        """
        changed = False
        for file_path in file_paths:
            if file_path in self.file_hashes:
                del self.file_hashes[file_path]
                changed = True
        
        if changed:
            self._dirty = True
    
    @staticmethod
    def create_file_hash(content: str) -> str:
        """Create a hash for file content.
        
        Args:
            content: File content
            
        Returns:
            SHA256 hash of the content
        """
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    @staticmethod
    async def create_file_hash_from_path(file_path: str) -> str:
        """Create a hash for a file by reading its content.
        
        Args:
            file_path: Path to the file
            
        Returns:
            SHA256 hash of the file content
        """
        try:
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                return CacheManager.create_file_hash(content)
        except Exception:
            # If we can't read the file, return a unique hash based on path and timestamp
            import time
            return hashlib.sha256(f"{file_path}-{time.time()}".encode()).hexdigest()
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.save()

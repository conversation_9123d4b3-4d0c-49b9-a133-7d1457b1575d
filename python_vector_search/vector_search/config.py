"""Configuration management for the vector search package."""

import os
from typing import Optional, Dict, Any
from dataclasses import dataclass, field
from pathlib import Path

from .models import EmbedderProvider
from .constants import (
    DEFAULT_SEARCH_MIN_SCORE,
    DEFAULT_MAX_SEARCH_RESULTS,
    DEFAULT_EMBEDDING_MODELS,
    MAX_FILE_SIZE_BYTES,
    BATCH_SEGMENT_THRESHOLD,
    PARSING_CONCURRENCY
)


@dataclass
class Config:
    """Configuration for the vector search engine."""
    
    # Embedder configuration
    embedder_provider: EmbedderProvider = EmbedderProvider.OPENAI
    model_id: Optional[str] = None
    model_dimension: Optional[int] = None
    
    # OpenAI configuration
    openai_api_key: Optional[str] = None
    openai_base_url: Optional[str] = None
    openai_organization: Optional[str] = None
    
    # Ollama configuration  
    ollama_base_url: str = "http://localhost:11434"
    ollama_model_id: Optional[str] = None
    
    # OpenAI Compatible configuration
    openai_compatible_base_url: Optional[str] = None
    openai_compatible_api_key: Optional[str] = None
    
    # Gemini configuration
    gemini_api_key: Optional[str] = None
    
    # LanceDB configuration
    lancedb_path: str = "./vector_db"
    table_name: str = "code_vectors"
    
    # Search configuration
    search_min_score: float = DEFAULT_SEARCH_MIN_SCORE
    search_max_results: int = DEFAULT_MAX_SEARCH_RESULTS
    
    # File processing configuration
    max_file_size_bytes: int = MAX_FILE_SIZE_BYTES
    batch_segment_threshold: int = BATCH_SEGMENT_THRESHOLD
    parsing_concurrency: int = PARSING_CONCURRENCY
    
    # Cache configuration
    cache_enabled: bool = True
    cache_path: Optional[str] = None
    
    # Ignore patterns
    ignore_patterns: list = field(default_factory=lambda: [
        ".git/**",
        "node_modules/**", 
        "__pycache__/**",
        "*.pyc",
        ".venv/**",
        "venv/**",
        ".env/**",
        "env/**",
        "build/**",
        "dist/**",
        "target/**",
        ".next/**",
        ".nuxt/**",
        "coverage/**",
        "*.log",
        "*.tmp",
        "*.temp"
    ])
    
    def __post_init__(self):
        """Post-initialization validation and setup."""
        # Load from environment variables if not provided
        if not self.openai_api_key:
            self.openai_api_key = os.getenv("OPENAI_API_KEY")
            
        if not self.gemini_api_key:
            self.gemini_api_key = os.getenv("GEMINI_API_KEY")
            
        if not self.openai_compatible_api_key:
            self.openai_compatible_api_key = os.getenv("OPENAI_COMPATIBLE_API_KEY")
            
        # Set default model and dimension if not provided
        if not self.model_id or not self.model_dimension:
            defaults = DEFAULT_EMBEDDING_MODELS.get(self.embedder_provider.value, {})
            if not self.model_id:
                self.model_id = defaults.get("model")
            if not self.model_dimension:
                self.model_dimension = defaults.get("dimension")
                
        # Set default cache path
        if self.cache_enabled and not self.cache_path:
            self.cache_path = str(Path(self.lancedb_path).parent / "cache.json")
    
    def validate(self) -> None:
        """Validate the configuration."""
        if self.embedder_provider == EmbedderProvider.OPENAI:
            if not self.openai_api_key:
                raise ValueError("OpenAI API key is required for OpenAI embedder")
                
        elif self.embedder_provider == EmbedderProvider.OLLAMA:
            if not self.ollama_base_url:
                raise ValueError("Ollama base URL is required for Ollama embedder")
                
        elif self.embedder_provider == EmbedderProvider.OPENAI_COMPATIBLE:
            if not self.openai_compatible_base_url or not self.openai_compatible_api_key:
                raise ValueError("Base URL and API key are required for OpenAI compatible embedder")
                
        elif self.embedder_provider == EmbedderProvider.GEMINI:
            if not self.gemini_api_key:
                raise ValueError("Gemini API key is required for Gemini embedder")
        
        if not self.model_id:
            raise ValueError("Model ID is required")
            
        if not self.model_dimension or self.model_dimension <= 0:
            raise ValueError("Valid model dimension is required")
            
        if self.search_min_score < 0 or self.search_min_score > 1:
            raise ValueError("Search min score must be between 0 and 1")
            
        if self.search_max_results <= 0:
            raise ValueError("Search max results must be positive")
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "Config":
        """Create config from dictionary."""
        return cls(**config_dict)
    
    @classmethod
    def from_file(cls, config_path: str) -> "Config":
        """Load config from YAML or JSON file."""
        import yaml
        import json
        
        config_path = Path(config_path)
        if not config_path.exists():
            raise FileNotFoundError(f"Config file not found: {config_path}")
            
        with open(config_path, 'r', encoding='utf-8') as f:
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                config_dict = yaml.safe_load(f)
            elif config_path.suffix.lower() == '.json':
                config_dict = json.load(f)
            else:
                raise ValueError(f"Unsupported config file format: {config_path.suffix}")
                
        return cls.from_dict(config_dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return {
            field.name: getattr(self, field.name)
            for field in self.__dataclass_fields__.values()
        }
    
    def save_to_file(self, config_path: str) -> None:
        """Save config to YAML or JSON file."""
        import yaml
        import json
        
        config_path = Path(config_path)
        config_dict = self.to_dict()
        
        # Remove sensitive information
        sensitive_keys = [
            'openai_api_key', 'gemini_api_key', 'openai_compatible_api_key'
        ]
        for key in sensitive_keys:
            if key in config_dict:
                config_dict[key] = "***"
        
        with open(config_path, 'w', encoding='utf-8') as f:
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                yaml.safe_dump(config_dict, f, default_flow_style=False)
            elif config_path.suffix.lower() == '.json':
                json.dump(config_dict, f, indent=2)
            else:
                raise ValueError(f"Unsupported config file format: {config_path.suffix}")

"""Search service for semantic code search."""

import os
from pathlib import Path
from typing import List, Optional, Dict, Any

from .models import SearchResult
from .embedders.base import BaseEmbedder
from .storage.base import BaseVectorStore
from .constants import DEFAULT_SEARCH_MIN_SCORE, DEFAULT_MAX_SEARCH_RESULTS


class SearchService:
    """Service for performing semantic search on indexed code."""
    
    def __init__(
        self,
        embedder: BaseEmbedder,
        vector_store: BaseVectorStore,
        min_score: float = DEFAULT_SEARCH_MIN_SCORE,
        max_results: int = DEFAULT_MAX_SEARCH_RESULTS
    ):
        """Initialize search service.
        
        Args:
            embedder: Embedder instance for query embedding
            vector_store: Vector store instance for search
            min_score: Default minimum score threshold
            max_results: Default maximum number of results
        """
        self.embedder = embedder
        self.vector_store = vector_store
        self.default_min_score = min_score
        self.default_max_results = max_results
    
    async def search(
        self,
        query: str,
        directory_prefix: Optional[str] = None,
        min_score: Optional[float] = None,
        max_results: Optional[int] = None,
        file_extensions: Optional[List[str]] = None,
        include_metadata: bool = True
    ) -> List[SearchResult]:
        """Perform semantic search on indexed code.
        
        Args:
            query: Search query text
            directory_prefix: Optional directory prefix to filter results
            min_score: Optional minimum score threshold
            max_results: Optional maximum number of results
            file_extensions: Optional list of file extensions to filter by
            include_metadata: Whether to include metadata in results
            
        Returns:
            List of search results sorted by relevance score
        """
        if not query.strip():
            return []
        
        # Use defaults if not provided
        min_score = min_score if min_score is not None else self.default_min_score
        max_results = max_results if max_results is not None else self.default_max_results
        
        try:
            # Generate embedding for query
            embedding_response = await self.embedder.create_embeddings([query])
            
            if not embedding_response.embeddings:
                raise ValueError("Failed to generate embedding for query")
            
            query_vector = embedding_response.embeddings[0]
            
            # Normalize directory prefix
            normalized_prefix = None
            if directory_prefix:
                normalized_prefix = os.path.normpath(directory_prefix)
            
            # Perform vector search
            results = await self.vector_store.search(
                query_vector=query_vector,
                directory_prefix=normalized_prefix,
                min_score=min_score,
                max_results=max_results
            )
            
            # Apply additional filters
            filtered_results = self._apply_filters(results, file_extensions)
            
            # Sort by score (descending)
            filtered_results.sort(key=lambda x: x.score, reverse=True)
            
            # Remove metadata if not requested
            if not include_metadata:
                for result in filtered_results:
                    result.metadata = None
            
            return filtered_results
            
        except Exception as e:
            raise RuntimeError(f"Search failed: {str(e)}")
    
    def _apply_filters(
        self,
        results: List[SearchResult],
        file_extensions: Optional[List[str]] = None
    ) -> List[SearchResult]:
        """Apply additional filters to search results.
        
        Args:
            results: List of search results
            file_extensions: Optional list of file extensions to filter by
            
        Returns:
            Filtered list of search results
        """
        if not file_extensions:
            return results
        
        # Normalize extensions (ensure they start with '.')
        normalized_extensions = []
        for ext in file_extensions:
            if not ext.startswith('.'):
                ext = '.' + ext
            normalized_extensions.append(ext.lower())
        
        # Filter by file extension
        filtered_results = []
        for result in results:
            file_ext = Path(result.file_path).suffix.lower()
            if file_ext in normalized_extensions:
                filtered_results.append(result)
        
        return filtered_results
    
    async def search_similar_code(
        self,
        code_snippet: str,
        min_score: Optional[float] = None,
        max_results: Optional[int] = None,
        exclude_file: Optional[str] = None
    ) -> List[SearchResult]:
        """Search for code similar to the provided snippet.
        
        Args:
            code_snippet: Code snippet to find similar code for
            min_score: Optional minimum score threshold
            max_results: Optional maximum number of results
            exclude_file: Optional file path to exclude from results
            
        Returns:
            List of similar code results
        """
        results = await self.search(
            query=code_snippet,
            min_score=min_score,
            max_results=max_results
        )
        
        # Exclude specified file if provided
        if exclude_file:
            results = [r for r in results if r.file_path != exclude_file]
        
        return results
    
    async def search_by_function_name(
        self,
        function_name: str,
        min_score: Optional[float] = None,
        max_results: Optional[int] = None
    ) -> List[SearchResult]:
        """Search for functions by name.
        
        Args:
            function_name: Function name to search for
            min_score: Optional minimum score threshold
            max_results: Optional maximum number of results
            
        Returns:
            List of function search results
        """
        # Create a query that focuses on function definitions
        query = f"function {function_name} definition implementation"
        
        results = await self.search(
            query=query,
            min_score=min_score,
            max_results=max_results
        )
        
        # Filter results to prioritize those with matching identifiers
        prioritized_results = []
        other_results = []
        
        for result in results:
            if (result.metadata and 
                result.metadata.get("identifier") and 
                function_name.lower() in result.metadata["identifier"].lower()):
                prioritized_results.append(result)
            else:
                other_results.append(result)
        
        # Return prioritized results first
        return prioritized_results + other_results
    
    async def search_by_class_name(
        self,
        class_name: str,
        min_score: Optional[float] = None,
        max_results: Optional[int] = None
    ) -> List[SearchResult]:
        """Search for classes by name.
        
        Args:
            class_name: Class name to search for
            min_score: Optional minimum score threshold
            max_results: Optional maximum number of results
            
        Returns:
            List of class search results
        """
        # Create a query that focuses on class definitions
        query = f"class {class_name} definition implementation"
        
        results = await self.search(
            query=query,
            min_score=min_score,
            max_results=max_results
        )
        
        # Filter results to prioritize those with matching identifiers
        prioritized_results = []
        other_results = []
        
        for result in results:
            if (result.metadata and 
                result.metadata.get("identifier") and 
                class_name.lower() in result.metadata["identifier"].lower()):
                prioritized_results.append(result)
            else:
                other_results.append(result)
        
        return prioritized_results + other_results
    
    async def get_search_suggestions(
        self,
        partial_query: str,
        max_suggestions: int = 5
    ) -> List[str]:
        """Get search suggestions based on partial query.
        
        Args:
            partial_query: Partial search query
            max_suggestions: Maximum number of suggestions
            
        Returns:
            List of search suggestions
        """
        # This is a simple implementation - in a real system you might
        # want to use a more sophisticated approach like analyzing
        # common patterns in the indexed code
        
        if len(partial_query) < 2:
            return []
        
        # Generate some basic suggestions
        suggestions = []
        
        # Add common programming patterns
        common_patterns = [
            f"{partial_query} function",
            f"{partial_query} class",
            f"{partial_query} method",
            f"{partial_query} implementation",
            f"{partial_query} example",
        ]
        
        suggestions.extend(common_patterns[:max_suggestions])
        
        return suggestions[:max_suggestions]
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the indexed collection.
        
        Returns:
            Dictionary with collection statistics
        """
        try:
            return await self.vector_store.get_collection_info()
        except Exception as e:
            return {"error": str(e)}

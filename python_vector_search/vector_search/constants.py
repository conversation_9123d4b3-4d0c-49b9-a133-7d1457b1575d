"""Constants used throughout the vector search package."""

# Parser constants
MAX_BLOCK_CHARS = 1000
MIN_BLOCK_CHARS = 50
MIN_CHUNK_REMAINDER_CHARS = 200
MAX_CHARS_TOLERANCE_FACTOR = 1.15

# Search constants
DEFAULT_SEARCH_MIN_SCORE = 0.4
DEFAULT_MAX_SEARCH_RESULTS = 50

# File processing constants
MAX_FILE_SIZE_BYTES = 1 * 1024 * 1024  # 1MB
BATCH_SEGMENT_THRESHOLD = 60
MAX_BATCH_RETRIES = 3
INITIAL_RETRY_DELAY_MS = 500
PARSING_CONCURRENCY = 10

# Embedding constants
MAX_BATCH_TOKENS = 100000
MAX_ITEM_TOKENS = 8191
BATCH_PROCESSING_CONCURRENCY = 10

# Gemini specific constants
GEMINI_MAX_ITEM_TOKENS = 2048

# Supported file extensions
SUPPORTED_EXTENSIONS = {
    ".py", ".js", ".jsx", ".ts", ".tsx", ".rs", ".go", ".java", ".cpp", ".hpp",
    ".c", ".h", ".cs", ".rb", ".php", ".swift", ".kt", ".kts", ".ex", ".exs",
    ".el", ".html", ".htm", ".md", ".markdown", ".json", ".css", ".rdl",
    ".ml", ".mli", ".lua", ".scala", ".toml", ".zig", ".elm", ".ejs", ".erb",
    ".vue", ".sol", ".tla"
}

# Language mappings for tree-sitter
LANGUAGE_MAPPINGS = {
    ".py": "python",
    ".js": "javascript", 
    ".jsx": "javascript",
    ".ts": "typescript",
    ".tsx": "tsx",
    ".rs": "rust",
    ".go": "go",
    ".java": "java",
    ".cpp": "cpp",
    ".hpp": "cpp", 
    ".c": "c",
    ".h": "c",
    ".cs": "c_sharp",
    ".rb": "ruby",
    ".php": "php",
    ".swift": "swift",
    ".kt": "kotlin",
    ".kts": "kotlin",
    ".ex": "elixir",
    ".exs": "elixir",
    ".el": "elisp",
    ".html": "html",
    ".htm": "html",
    ".css": "css",
    ".lua": "lua",
    ".scala": "scala",
    ".toml": "toml",
    ".zig": "zig",
    ".elm": "elm",
    ".sol": "solidity",
    ".tla": "tlaplus",
    ".vue": "vue",
    ".ml": "ocaml",
    ".mli": "ocaml",
}

# Default embedding model configurations
DEFAULT_EMBEDDING_MODELS = {
    "openai": {
        "model": "text-embedding-3-small",
        "dimension": 1536
    },
    "ollama": {
        "model": "nomic-embed-text:latest", 
        "dimension": 768
    },
    "gemini": {
        "model": "text-embedding-004",
        "dimension": 768
    }
}

"""Data models for the vector search package."""

from dataclasses import dataclass
from typing import Optional, Dict, Any, List
from enum import Enum


class EmbedderProvider(str, Enum):
    """Supported embedding providers."""
    OPENAI = "openai"
    OLLAMA = "ollama"
    OPENAI_COMPATIBLE = "openai_compatible"
    GEMINI = "gemini"


@dataclass
class CodeBlock:
    """Represents a parsed code block."""
    file_path: str
    identifier: Optional[str]
    type: str
    start_line: int
    end_line: int
    content: str
    segment_hash: str
    file_hash: str


@dataclass
class SearchResult:
    """Represents a search result."""
    id: str
    score: float
    file_path: str
    code_chunk: str
    start_line: int
    end_line: int
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class EmbeddingResponse:
    """Response from embedding provider."""
    embeddings: List[List[float]]
    usage: Optional[Dict[str, int]] = None


@dataclass
class BatchProcessingSummary:
    """Summary of batch processing results."""
    total_files: int
    processed_files: int
    total_blocks: int
    indexed_blocks: int
    errors: List[str]
    processing_time: float


@dataclass
class FileProcessingResult:
    """Result of processing a single file."""
    file_path: str
    blocks: List[CodeBlock]
    success: bool
    error: Optional[str] = None
    processing_time: float = 0.0

# Vector Search Package

A comprehensive Python package for code indexing, vectorization, and semantic search using LanceDB. This package converts the TypeScript-based vector search functionality into a standalone Python implementation.

## Features

- **Multi-language Code Parsing**: Support for Python, JavaScript, TypeScript, Rust, Go, Java, C++, C#, and more
- **Flexible Embedding Providers**: OpenAI, Ollama, OpenAI-compatible APIs, and Google Gemini
- **Efficient Vector Storage**: LanceDB for high-performance vector storage and retrieval
- **Incremental Updates**: Smart caching to avoid reprocessing unchanged files
- **Batch Processing**: Optimized batch processing for large codebases
- **Semantic Search**: Advanced semantic search with configurable scoring thresholds
- **Tree-sitter Integration**: Advanced code parsing using tree-sitter for accurate code structure analysis
- **Markdown Support**: Special handling for markdown files with code blocks and sections

## Installation

```bash
pip install vector-search
```

For development:

```bash
pip install vector-search[dev]
```

## Quick Start

```python
import asyncio
from vector_search import VectorSearchEngine, Config
from vector_search.models import EmbedderProvider

async def main():
    # Initialize configuration
    config = Config(
        embedder_provider=EmbedderProvider.OPENAI,
        openai_api_key="your-api-key",
        lancedb_path="./vector_db",
        model_id="text-embedding-3-small",
        model_dimension=1536
    )

    # Create search engine
    engine = VectorSearchEngine(config)

    # Index a directory
    summary = await engine.index_directory("/path/to/code")
    print(f"Indexed {summary.indexed_blocks} code blocks from {summary.processed_files} files")

    # Search for code
    results = await engine.search("function to handle user authentication")

    for result in results:
        print(f"File: {result.file_path}:{result.start_line}-{result.end_line}")
        print(f"Score: {result.score:.3f}")
        print(f"Code: {result.code_chunk[:100]}...")
        print()

    await engine.close()

# Run the example
asyncio.run(main())
```

## Supported Languages

The package supports parsing and indexing of the following programming languages:

- **Python** (.py)
- **JavaScript** (.js, .jsx)
- **TypeScript** (.ts, .tsx)
- **Rust** (.rs)
- **Go** (.go)
- **Java** (.java)
- **C++** (.cpp, .hpp)
- **C** (.c, .h)
- **C#** (.cs)
- **Ruby** (.rb)
- **PHP** (.php)
- **Swift** (.swift)
- **Kotlin** (.kt, .kts)
- **Elixir** (.ex, .exs)
- **HTML** (.html, .htm)
- **CSS** (.css)
- **Markdown** (.md, .markdown)
- **JSON** (.json)
- **TOML** (.toml)
- **And more...**

## Embedding Providers

### OpenAI

```python
config = Config(
    embedder_provider=EmbedderProvider.OPENAI,
    openai_api_key="your-openai-api-key",
    model_id="text-embedding-3-small",
    model_dimension=1536
)
```

### Ollama (Local)

```python
config = Config(
    embedder_provider=EmbedderProvider.OLLAMA,
    ollama_base_url="http://localhost:11434",
    model_id="nomic-embed-text:latest",
    model_dimension=768
)
```

### Google Gemini

```python
config = Config(
    embedder_provider=EmbedderProvider.GEMINI,
    gemini_api_key="your-gemini-api-key",
    # Model and dimension are fixed for Gemini
)
```

### OpenAI-Compatible APIs

```python
config = Config(
    embedder_provider=EmbedderProvider.OPENAI_COMPATIBLE,
    openai_compatible_base_url="https://api.your-provider.com/v1",
    openai_compatible_api_key="your-api-key",
    model_id="your-model-id",
    model_dimension=1024
)
```

## Configuration

### From Dictionary

```python
config_dict = {
    "embedder_provider": "openai",
    "openai_api_key": "your-key",
    "model_id": "text-embedding-3-small",
    "model_dimension": 1536,
    "lancedb_path": "./my_vector_db",
    "search_min_score": 0.3,
    "search_max_results": 50,
    "ignore_patterns": [
        ".git/**",
        "node_modules/**",
        "__pycache__/**"
    ]
}

config = Config.from_dict(config_dict)
```

### From File

```python
# Save configuration
config.save_to_file("config.json")

# Load configuration
config = Config.from_file("config.json")
```

### Environment Variables

The package automatically loads API keys from environment variables:

```bash
export OPENAI_API_KEY="your-openai-key"
export GEMINI_API_KEY="your-gemini-key"
export OPENAI_COMPATIBLE_API_KEY="your-compatible-key"
```

## Advanced Usage

### Custom Ignore Patterns

```python
config = Config(
    # ... other settings ...
    ignore_patterns=[
        # Standard patterns
        ".git/**",
        "node_modules/**",
        "__pycache__/**",
        "*.pyc",

        # Custom patterns
        "build/**",
        "dist/**",
        "coverage/**",
        "docs/build/**",
        "tests/fixtures/**",

        # Language-specific
        "target/**",      # Rust
        "bin/**",         # Go
        ".next/**",       # Next.js
    ]
)
```

### Progress Tracking

```python
def progress_callback(processed, total, current_file):
    print(f"Processing {processed}/{total}: {current_file}")

def block_callback(blocks_processed):
    print(f"Indexed {blocks_processed} code blocks")

summary = await engine.index_directory(
    "/path/to/code",
    progress_callback=progress_callback,
    block_callback=block_callback
)
```

### Advanced Search Options

```python
# Search with filters
results = await engine.search(
    "authentication function",
    directory_prefix="src/auth",
    file_extensions=[".py", ".js"],
    min_score=0.5,
    max_results=10
)

# Search for similar code
results = await engine.search_similar_code(
    "def authenticate(username, password):",
    exclude_file="/path/to/current/file.py"
)

# Search by function name
results = await engine.search_by_function_name("authenticate")

# Search by class name
results = await engine.search_by_class_name("UserManager")
```

## Command Line Interface

The package includes a command-line interface for easy usage:

```bash
# Index a directory
vector-search index /path/to/code --embedder openai --openai-key your-key

# Search indexed code
vector-search search "authentication function" --show-code

# Search with filters
vector-search search "database connection" --extensions .py,.js --min-score 0.5

# Get collection statistics
vector-search stats

# Clear the index
vector-search clear --force
```

### CLI Options

```bash
vector-search --help

# Global options:
--embedder {openai,ollama,openai_compatible,gemini}
--openai-key OPENAI_KEY
--ollama-url OLLAMA_URL
--gemini-key GEMINI_KEY
--model MODEL
--dimension DIMENSION
--db-path DB_PATH
--min-score MIN_SCORE
--max-results MAX_RESULTS
--verbose

# Commands:
index DIRECTORY          # Index a directory
search QUERY             # Search indexed code
stats                    # Show collection statistics
clear                    # Clear the index
```

## API Reference

### VectorSearchEngine

Main class for vector search operations.

```python
engine = VectorSearchEngine(config)

# Initialize the engine
await engine.initialize()

# Index operations
summary = await engine.index_directory(directory_path)
success = await engine.index_file(file_path)

# Search operations
results = await engine.search(query, **options)
results = await engine.search_similar_code(code_snippet)
results = await engine.search_by_function_name(function_name)
results = await engine.search_by_class_name(class_name)

# Management operations
await engine.clear_index()
stats = await engine.get_stats()
await engine.close()
```

### Config

Configuration class for the vector search engine.

```python
config = Config(
    embedder_provider=EmbedderProvider.OPENAI,
    openai_api_key="your-key",
    model_id="text-embedding-3-small",
    model_dimension=1536,
    lancedb_path="./vector_db",
    table_name="code_vectors",
    search_min_score=0.4,
    search_max_results=50,
    cache_enabled=True,
    ignore_patterns=["*.pyc", "__pycache__/**"]
)

# Validation
config.validate()

# Serialization
config_dict = config.to_dict()
config.save_to_file("config.json")
config = Config.from_file("config.json")
```

### Models

Data models used throughout the package.

```python
from vector_search.models import (
    SearchResult,
    CodeBlock,
    BatchProcessingSummary,
    EmbedderProvider
)

# SearchResult
result = SearchResult(
    id="unique-id",
    score=0.85,
    file_path="/path/to/file.py",
    code_chunk="def example(): pass",
    start_line=10,
    end_line=12,
    metadata={"type": "function"}
)

# CodeBlock
block = CodeBlock(
    file_path="/path/to/file.py",
    identifier="example_function",
    type="function_definition",
    start_line=10,
    end_line=15,
    content="def example(): pass",
    segment_hash="abc123",
    file_hash="def456"
)
```

## Performance Considerations

### Batch Processing

The package automatically batches operations for optimal performance:

```python
config = Config(
    # ... other settings ...
    batch_segment_threshold=60,    # Process 60 code blocks per batch
    parsing_concurrency=10,        # Parse 10 files concurrently
    max_file_size_bytes=1048576   # Skip files larger than 1MB
)
```

### Caching

Enable caching to avoid reprocessing unchanged files:

```python
config = Config(
    # ... other settings ...
    cache_enabled=True,
    cache_path="./cache.json"
)
```

### Memory Usage

For large codebases, consider:

- Using smaller batch sizes
- Limiting concurrent processing
- Using more restrictive ignore patterns
- Processing directories incrementally

## Examples

See the `examples/` directory for complete usage examples:

- `basic_usage.py` - Basic indexing and searching
- `advanced_usage.py` - Advanced configuration and search options
- `cli_example.py` - Command-line interface usage

## Testing

Run the test suite:

```bash
# Install development dependencies
pip install -e .[dev]

# Run tests
pytest

# Run tests with coverage
pytest --cov=vector_search

# Run specific test file
pytest tests/test_config.py
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Acknowledgments

This package is inspired by and converts functionality from the TypeScript-based Roo-Code vector search implementation. It provides a standalone Python solution for code indexing and semantic search using modern vector database technology.

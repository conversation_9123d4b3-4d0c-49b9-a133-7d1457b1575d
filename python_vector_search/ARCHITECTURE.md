# Vector Search Package Architecture

This document describes the architecture and design of the Python vector search package.

## Overview

The vector search package is a comprehensive solution for indexing and searching code repositories using semantic vector search. It converts the TypeScript-based functionality from the Roo-Code project into a standalone Python implementation using LanceDB for vector storage.

## Architecture Components

### 1. Core Engine (`engine.py`)

The `VectorSearchEngine` is the main orchestrator that coordinates all components:

- **Initialization**: Sets up all components and validates configuration
- **Indexing**: Coordinates file scanning, parsing, embedding, and storage
- **Searching**: Handles query embedding and vector search operations
- **Management**: Provides collection statistics and cleanup operations

### 2. Configuration Management (`config.py`)

The `Config` class manages all configuration options:

- **Provider Settings**: Embedder provider configuration (OpenAI, Ollama, Gemini, etc.)
- **Storage Settings**: LanceDB path, table name, cache settings
- **Search Settings**: Score thresholds, result limits, ignore patterns
- **Serialization**: Save/load configuration from JSON/YAML files
- **Validation**: Ensures all required settings are present and valid

### 3. Embedder Providers (`embedders/`)

Modular embedder implementations supporting multiple providers:

#### Base Interface (`base.py`)
- Abstract base class defining the embedder interface
- Token estimation and text chunking utilities
- Consistent error handling and validation

#### Provider Implementations
- **OpenAI** (`openai_embedder.py`): Official OpenAI API
- **Ollama** (`ollama_embedder.py`): Local Ollama instance
- **OpenAI Compatible** (`openai_compatible_embedder.py`): OpenAI-compatible APIs
- **Gemini** (`gemini_embedder.py`): Google Gemini API

Each provider handles:
- Authentication and configuration validation
- Batch processing with retry logic
- Rate limiting and error handling
- Model-specific optimizations

### 4. Code Parsers (`parsers/`)

Language-aware code parsing for accurate code structure extraction:

#### Tree-sitter Parser (`tree_sitter_parser.py`)
- Multi-language support using tree-sitter
- AST-based code block extraction
- Intelligent chunking for large code blocks
- Fallback chunking when parsers aren't available

#### Markdown Parser (`markdown_parser.py`)
- Specialized markdown parsing
- Code block extraction with language detection
- Section-based content organization
- Header and content separation

#### Features
- Support for 20+ programming languages
- Configurable block size limits
- Duplicate detection using content hashing
- Metadata extraction (function names, class names, etc.)

### 5. Vector Storage (`storage/`)

LanceDB-based vector storage with high-performance operations:

#### LanceDB Store (`lancedb_store.py`)
- **Schema Management**: Structured data storage with metadata
- **Vector Operations**: Efficient similarity search with filtering
- **Batch Operations**: Optimized bulk insert/update/delete
- **Path Filtering**: Directory-based result filtering
- **Collection Management**: Create, clear, delete operations

#### Features
- Automatic schema creation and validation
- Cosine similarity search
- Metadata filtering and path-based queries
- Efficient batch processing
- Collection statistics and monitoring

### 6. File Processing (`scanner.py`)

Intelligent file discovery and processing:

#### Directory Scanner
- **File Discovery**: Recursive directory traversal with ignore patterns
- **Change Detection**: Hash-based incremental processing
- **Batch Processing**: Concurrent file processing with progress tracking
- **Error Handling**: Graceful error handling with detailed reporting

#### Features
- Gitignore-style ignore patterns
- File size and extension filtering
- Concurrent processing with configurable limits
- Progress callbacks for UI integration
- Comprehensive error reporting

### 7. Caching System (`cache.py`)

Efficient caching to avoid reprocessing unchanged files:

#### Cache Manager
- **Hash Storage**: File content hash tracking
- **Change Detection**: Identify modified, new, and deleted files
- **Persistence**: JSON-based cache storage
- **Atomic Operations**: Safe concurrent access

#### Features
- SHA256 content hashing
- Atomic file operations
- Batch cache updates
- Automatic cleanup of deleted files
- Configurable cache location

### 8. Search Service (`search.py`)

Advanced semantic search capabilities:

#### Search Operations
- **Semantic Search**: Natural language query processing
- **Code Similarity**: Find similar code snippets
- **Symbol Search**: Function and class name search
- **Filtered Search**: Directory and file extension filtering

#### Features
- Configurable scoring thresholds
- Result ranking and sorting
- Metadata-based filtering
- Search suggestions
- Collection statistics

## Data Flow

### Indexing Process

1. **File Discovery**: Scanner finds all supported files in directory
2. **Change Detection**: Cache manager identifies changed files
3. **Parsing**: Code parsers extract structured code blocks
4. **Embedding**: Embedder generates vector representations
5. **Storage**: Vector store saves embeddings with metadata
6. **Cache Update**: Cache manager updates file hashes

### Search Process

1. **Query Processing**: Search service receives natural language query
2. **Embedding**: Embedder generates query vector
3. **Vector Search**: Vector store performs similarity search
4. **Filtering**: Results filtered by directory, extension, score
5. **Ranking**: Results sorted by relevance score
6. **Response**: Formatted results returned to user

## Design Principles

### 1. Modularity
- Clear separation of concerns
- Pluggable components (embedders, parsers, storage)
- Well-defined interfaces and abstractions

### 2. Performance
- Batch processing for efficiency
- Concurrent operations where safe
- Intelligent caching to avoid redundant work
- Optimized vector operations

### 3. Reliability
- Comprehensive error handling
- Graceful degradation
- Atomic operations for data consistency
- Extensive validation

### 4. Extensibility
- Plugin architecture for new embedders
- Support for additional languages
- Configurable processing parameters
- Flexible ignore patterns

### 5. Usability
- Simple high-level API
- Comprehensive configuration options
- Progress tracking and feedback
- Clear error messages

## Configuration Architecture

### Hierarchical Configuration
1. **Default Values**: Sensible defaults for all options
2. **Environment Variables**: API keys and sensitive data
3. **Configuration Files**: Persistent settings
4. **Runtime Parameters**: Dynamic overrides

### Validation Pipeline
1. **Schema Validation**: Type checking and required fields
2. **Provider Validation**: Embedder-specific requirements
3. **Connectivity Testing**: Verify external service access
4. **Resource Validation**: Check file paths and permissions

## Error Handling Strategy

### Graceful Degradation
- Continue processing when individual files fail
- Provide detailed error reporting
- Maintain partial results when possible

### Retry Logic
- Exponential backoff for rate limits
- Configurable retry attempts
- Circuit breaker patterns for failing services

### User Feedback
- Progress indicators for long operations
- Clear error messages with suggestions
- Comprehensive logging for debugging

## Testing Strategy

### Unit Tests
- Individual component testing
- Mock external dependencies
- Edge case validation

### Integration Tests
- End-to-end workflow testing
- Real embedder integration
- Performance benchmarking

### Test Fixtures
- Sample code repositories
- Mock embedder responses
- Configuration test cases

## Future Enhancements

### Planned Features
- Additional embedder providers
- More programming language support
- Advanced search operators
- Real-time file watching
- Distributed processing

### Performance Optimizations
- Vector quantization
- Incremental indexing
- Parallel processing improvements
- Memory usage optimization

This architecture provides a solid foundation for semantic code search while maintaining flexibility for future enhancements and integrations.

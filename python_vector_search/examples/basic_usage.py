#!/usr/bin/env python3
"""Basic usage example for the vector search package."""

import asyncio
import os
from pathlib import Path

from vector_search import VectorSearchEngine, Config
from vector_search.models import EmbedderProvider


async def main():
    """Main example function."""
    
    # Create configuration
    config = Config(
        # Embedder configuration
        embedder_provider=EmbedderProvider.OPENAI,
        openai_api_key=os.getenv("OPENAI_API_KEY"),  # Set your API key
        model_id="text-embedding-3-small",
        model_dimension=1536,
        
        # Storage configuration
        lancedb_path="./example_vector_db",
        table_name="code_vectors",
        
        # Search configuration
        search_min_score=0.3,
        search_max_results=20,
        
        # Cache configuration
        cache_enabled=True,
        cache_path="./example_cache.json",
        
        # Ignore patterns (gitignore style)
        ignore_patterns=[
            ".git/**",
            "node_modules/**",
            "__pycache__/**",
            "*.pyc",
            ".venv/**",
            "build/**",
            "dist/**",
            "*.log"
        ]
    )
    
    # Validate configuration
    try:
        config.validate()
        print("✓ Configuration is valid")
    except ValueError as e:
        print(f"✗ Configuration error: {e}")
        return
    
    # Create search engine
    engine = VectorSearchEngine(config)
    
    try:
        # Initialize the engine
        print("Initializing vector search engine...")
        collection_created = await engine.initialize()
        
        if collection_created:
            print("✓ Created new vector collection")
        else:
            print("✓ Using existing vector collection")
        
        # Example 1: Index a directory
        print("\n=== Indexing Directory ===")
        
        # Create example directory with some code files
        example_dir = Path("./example_code")
        example_dir.mkdir(exist_ok=True)
        
        # Create sample Python file
        python_file = example_dir / "calculator.py"
        python_file.write_text('''
def add(a, b):
    """Add two numbers together."""
    return a + b

def multiply(a, b):
    """Multiply two numbers."""
    return a * b

def fibonacci(n):
    """Calculate the nth Fibonacci number."""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

class Calculator:
    """A simple calculator class."""
    
    def __init__(self):
        self.history = []
    
    def calculate(self, operation, a, b):
        """Perform a calculation and store in history."""
        if operation == "add":
            result = add(a, b)
        elif operation == "multiply":
            result = multiply(a, b)
        else:
            raise ValueError(f"Unknown operation: {operation}")
        
        self.history.append((operation, a, b, result))
        return result
''')
        
        # Create sample JavaScript file
        js_file = example_dir / "utils.js"
        js_file.write_text('''
/**
 * Utility functions for mathematical operations
 */

function factorial(n) {
    // Calculate factorial of n
    if (n <= 1) {
        return 1;
    }
    return n * factorial(n - 1);
}

function isPrime(num) {
    // Check if a number is prime
    if (num <= 1) return false;
    if (num <= 3) return true;
    if (num % 2 === 0 || num % 3 === 0) return false;
    
    for (let i = 5; i * i <= num; i += 6) {
        if (num % i === 0 || num % (i + 2) === 0) {
            return false;
        }
    }
    return true;
}

class MathUtils {
    static gcd(a, b) {
        // Greatest common divisor using Euclidean algorithm
        while (b !== 0) {
            let temp = b;
            b = a % b;
            a = temp;
        }
        return a;
    }
}
''')
        
        # Create README file
        readme_file = example_dir / "README.md"
        readme_file.write_text('''
# Example Code Repository

This repository contains example mathematical functions and utilities.

## Python Module

The `calculator.py` module provides:

- Basic arithmetic functions (add, multiply)
- Fibonacci number calculation
- Calculator class for operation history

## JavaScript Module

The `utils.js` module provides:

- Factorial calculation
- Prime number checking
- Greatest common divisor calculation

## Usage

```python
from calculator import Calculator

calc = Calculator()
result = calc.calculate("add", 5, 3)
print(f"Result: {result}")
```

```javascript
const result = factorial(5);
console.log(`5! = ${result}`);
```
''')
        
        # Index the directory with progress tracking
        def progress_callback(processed, total, current_file):
            print(f"Processing {processed}/{total}: {current_file}")
        
        def block_callback(blocks_processed):
            if blocks_processed % 10 == 0:
                print(f"Indexed {blocks_processed} code blocks")
        
        summary = await engine.index_directory(
            str(example_dir),
            progress_callback=progress_callback,
            block_callback=block_callback
        )
        
        print(f"\n✓ Indexing complete!")
        print(f"  Total files: {summary.total_files}")
        print(f"  Processed files: {summary.processed_files}")
        print(f"  Total blocks: {summary.total_blocks}")
        print(f"  Indexed blocks: {summary.indexed_blocks}")
        print(f"  Processing time: {summary.processing_time:.2f}s")
        
        if summary.errors:
            print(f"  Errors: {len(summary.errors)}")
            for error in summary.errors[:3]:  # Show first 3 errors
                print(f"    - {error}")
        
        # Example 2: Perform searches
        print("\n=== Search Examples ===")
        
        # Search for functions
        print("\n1. Searching for 'fibonacci function':")
        results = await engine.search("fibonacci function", max_results=5)
        for i, result in enumerate(results, 1):
            print(f"  {i}. {result.file_path}:{result.start_line}-{result.end_line} (score: {result.score:.3f})")
            print(f"     {result.code_chunk[:100]}...")
        
        # Search for classes
        print("\n2. Searching for 'calculator class':")
        results = await engine.search("calculator class", max_results=3)
        for i, result in enumerate(results, 1):
            print(f"  {i}. {result.file_path}:{result.start_line}-{result.end_line} (score: {result.score:.3f})")
        
        # Search by function name
        print("\n3. Searching by function name 'factorial':")
        results = await engine.search_by_function_name("factorial", max_results=3)
        for i, result in enumerate(results, 1):
            print(f"  {i}. {result.file_path}:{result.start_line}-{result.end_line} (score: {result.score:.3f})")
        
        # Search similar code
        print("\n4. Searching for code similar to 'def add(a, b): return a + b':")
        results = await engine.search_similar_code("def add(a, b): return a + b", max_results=3)
        for i, result in enumerate(results, 1):
            print(f"  {i}. {result.file_path}:{result.start_line}-{result.end_line} (score: {result.score:.3f})")
        
        # Search with filters
        print("\n5. Searching for 'function' in Python files only:")
        results = await engine.search("function", file_extensions=[".py"], max_results=3)
        for i, result in enumerate(results, 1):
            print(f"  {i}. {result.file_path}:{result.start_line}-{result.end_line} (score: {result.score:.3f})")
        
        # Example 3: Get statistics
        print("\n=== Collection Statistics ===")
        stats = await engine.get_stats()
        print(f"Collection name: {stats.get('name', 'N/A')}")
        print(f"Total vectors: {stats.get('count', 'N/A')}")
        print(f"Database path: {stats.get('db_path', 'N/A')}")
        
        # Example 4: Index a single file
        print("\n=== Single File Indexing ===")
        
        # Create another file
        new_file = example_dir / "geometry.py"
        new_file.write_text('''
import math

def circle_area(radius):
    """Calculate the area of a circle."""
    return math.pi * radius ** 2

def triangle_area(base, height):
    """Calculate the area of a triangle."""
    return 0.5 * base * height
''')
        
        success = await engine.index_file(str(new_file))
        if success:
            print(f"✓ Successfully indexed {new_file}")
        else:
            print(f"✗ Failed to index {new_file}")
        
        # Search for the new content
        print("\nSearching for 'circle area':")
        results = await engine.search("circle area", max_results=2)
        for i, result in enumerate(results, 1):
            print(f"  {i}. {result.file_path}:{result.start_line}-{result.end_line} (score: {result.score:.3f})")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up
        await engine.close()
        print("\n✓ Engine closed successfully")


if __name__ == "__main__":
    # Run the example
    asyncio.run(main())

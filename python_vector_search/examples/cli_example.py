#!/usr/bin/env python3
"""Command-line interface example for the vector search package."""

import asyncio
import argparse
import sys
from pathlib import Path

from vector_search import VectorSearchEngine, Config
from vector_search.models import Embedder<PERSON>rovider


async def index_command(args):
    """Handle index command."""
    print(f"Indexing directory: {args.directory}")
    
    # Create configuration
    config = Config(
        embedder_provider=EmbedderProvider(args.embedder),
        openai_api_key=args.openai_key,
        ollama_base_url=args.ollama_url,
        gemini_api_key=args.gemini_key,
        model_id=args.model,
        model_dimension=args.dimension,
        lancedb_path=args.db_path,
        search_min_score=args.min_score,
        search_max_results=args.max_results
    )
    
    engine = VectorSearchEngine(config)
    
    try:
        await engine.initialize()
        
        def progress_callback(processed, total, current_file):
            if args.verbose:
                print(f"Processing {processed}/{total}: {current_file}")
            elif processed % 10 == 0 or processed == total:
                print(f"Progress: {processed}/{total} files")
        
        def block_callback(blocks):
            if args.verbose and blocks % 50 == 0:
                print(f"Indexed {blocks} code blocks")
        
        summary = await engine.index_directory(
            args.directory,
            progress_callback=progress_callback,
            block_callback=block_callback
        )
        
        print(f"\n✓ Indexing complete!")
        print(f"  Total files: {summary.total_files}")
        print(f"  Processed files: {summary.processed_files}")
        print(f"  Indexed blocks: {summary.indexed_blocks}")
        print(f"  Processing time: {summary.processing_time:.2f}s")
        
        if summary.errors:
            print(f"  Errors: {len(summary.errors)}")
            if args.verbose:
                for error in summary.errors:
                    print(f"    - {error}")
    
    except Exception as e:
        print(f"Error during indexing: {e}")
        sys.exit(1)
    finally:
        await engine.close()


async def search_command(args):
    """Handle search command."""
    print(f"Searching for: {args.query}")
    
    # Create configuration
    config = Config(
        embedder_provider=EmbedderProvider(args.embedder),
        openai_api_key=args.openai_key,
        ollama_base_url=args.ollama_url,
        gemini_api_key=args.gemini_key,
        model_id=args.model,
        model_dimension=args.dimension,
        lancedb_path=args.db_path,
        search_min_score=args.min_score,
        search_max_results=args.max_results
    )
    
    engine = VectorSearchEngine(config)
    
    try:
        await engine.initialize()
        
        # Parse file extensions if provided
        file_extensions = None
        if args.extensions:
            file_extensions = [ext.strip() for ext in args.extensions.split(',')]
        
        # Perform search
        results = await engine.search(
            query=args.query,
            directory_prefix=args.directory_filter,
            min_score=args.min_score,
            max_results=args.max_results,
            file_extensions=file_extensions
        )
        
        if not results:
            print("No results found.")
            return
        
        print(f"\nFound {len(results)} results:")
        print("-" * 80)
        
        for i, result in enumerate(results, 1):
            print(f"{i}. {result.file_path}:{result.start_line}-{result.end_line}")
            print(f"   Score: {result.score:.3f}")
            
            if args.show_code:
                # Show code snippet
                lines = result.code_chunk.split('\n')
                if len(lines) > 5:
                    # Show first few lines
                    for line in lines[:3]:
                        print(f"   | {line}")
                    print(f"   | ... ({len(lines)-3} more lines)")
                else:
                    for line in lines:
                        print(f"   | {line}")
            
            print()
    
    except Exception as e:
        print(f"Error during search: {e}")
        sys.exit(1)
    finally:
        await engine.close()


async def stats_command(args):
    """Handle stats command."""
    print("Getting collection statistics...")
    
    # Create configuration
    config = Config(
        embedder_provider=EmbedderProvider(args.embedder),
        openai_api_key=args.openai_key,
        ollama_base_url=args.ollama_url,
        gemini_api_key=args.gemini_key,
        model_id=args.model,
        model_dimension=args.dimension,
        lancedb_path=args.db_path
    )
    
    engine = VectorSearchEngine(config)
    
    try:
        await engine.initialize()
        
        stats = await engine.get_stats()
        
        print("\nCollection Statistics:")
        print("-" * 30)
        print(f"Collection name: {stats.get('name', 'N/A')}")
        print(f"Total vectors: {stats.get('count', 'N/A')}")
        print(f"Database path: {stats.get('db_path', 'N/A')}")
        
        if 'schema' in stats:
            schema = stats['schema']
            print(f"Vector dimension: {schema.get('dimension', 'N/A')}")
            print(f"Schema fields: {', '.join(schema.get('fields', []))}")
    
    except Exception as e:
        print(f"Error getting stats: {e}")
        sys.exit(1)
    finally:
        await engine.close()


async def clear_command(args):
    """Handle clear command."""
    if not args.force:
        response = input("Are you sure you want to clear the index? (y/N): ")
        if response.lower() != 'y':
            print("Operation cancelled.")
            return
    
    print("Clearing index...")
    
    # Create configuration
    config = Config(
        embedder_provider=EmbedderProvider(args.embedder),
        openai_api_key=args.openai_key,
        ollama_base_url=args.ollama_url,
        gemini_api_key=args.gemini_key,
        model_id=args.model,
        model_dimension=args.dimension,
        lancedb_path=args.db_path
    )
    
    engine = VectorSearchEngine(config)
    
    try:
        await engine.initialize()
        await engine.clear_index()
        print("✓ Index cleared successfully.")
    
    except Exception as e:
        print(f"Error clearing index: {e}")
        sys.exit(1)
    finally:
        await engine.close()


def main():
    """Main CLI function."""
    parser = argparse.ArgumentParser(
        description="Vector Search CLI - Index and search code repositories"
    )
    
    # Global arguments
    parser.add_argument("--embedder", choices=["openai", "ollama", "openai_compatible", "gemini"],
                       default="openai", help="Embedder provider to use")
    parser.add_argument("--openai-key", help="OpenAI API key")
    parser.add_argument("--ollama-url", default="http://localhost:11434", help="Ollama base URL")
    parser.add_argument("--gemini-key", help="Gemini API key")
    parser.add_argument("--model", help="Model ID to use")
    parser.add_argument("--dimension", type=int, help="Vector dimension")
    parser.add_argument("--db-path", default="./vector_db", help="Database path")
    parser.add_argument("--min-score", type=float, default=0.3, help="Minimum search score")
    parser.add_argument("--max-results", type=int, default=20, help="Maximum search results")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    
    # Subcommands
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Index command
    index_parser = subparsers.add_parser("index", help="Index a directory")
    index_parser.add_argument("directory", help="Directory to index")
    
    # Search command
    search_parser = subparsers.add_parser("search", help="Search indexed code")
    search_parser.add_argument("query", help="Search query")
    search_parser.add_argument("--directory-filter", help="Filter by directory prefix")
    search_parser.add_argument("--extensions", help="Filter by file extensions (comma-separated)")
    search_parser.add_argument("--show-code", action="store_true", help="Show code snippets")
    
    # Stats command
    stats_parser = subparsers.add_parser("stats", help="Show collection statistics")
    
    # Clear command
    clear_parser = subparsers.add_parser("clear", help="Clear the index")
    clear_parser.add_argument("--force", action="store_true", help="Force clear without confirmation")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    # Run the appropriate command
    if args.command == "index":
        asyncio.run(index_command(args))
    elif args.command == "search":
        asyncio.run(search_command(args))
    elif args.command == "stats":
        asyncio.run(stats_command(args))
    elif args.command == "clear":
        asyncio.run(clear_command(args))


if __name__ == "__main__":
    main()

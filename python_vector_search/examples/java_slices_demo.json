{"repository_path": "/home/<USER>/fengbin/DailyWork/ZeroAgents/competitor/Roo-Code/python_vector_search/examples/test_java_project", "summary": {"total_files": 3, "processed_files": 3, "total_blocks": 12, "success_rate": "100.0%"}, "files": {"/home/<USER>/fengbin/DailyWork/ZeroAgents/competitor/Roo-Code/python_vector_search/examples/test_java_project/Shape.java": [{"file_path": "/home/<USER>/fengbin/DailyWork/ZeroAgents/competitor/Roo-Code/python_vector_search/examples/test_java_project/Shape.java", "identifier": null, "type": "fallback", "start_line": 1, "end_line": 53, "line_count": 53, "content_preview": "package com.example.geometry;\n\n/**\n * Abstract base class for geometric shapes\n */\npublic abstract class Shape {\n    \n    protected String name;\n    protected String color;\n    \n    /**\n     * Constru...", "content_length": 1139, "segment_hash": "22aeaadc..."}, {"file_path": "/home/<USER>/fengbin/DailyWork/ZeroAgents/competitor/Roo-Code/python_vector_search/examples/test_java_project/Shape.java", "identifier": null, "type": "fallback", "start_line": 54, "end_line": 107, "line_count": 54, "content_preview": "        this.color = color;\n    }\n    \n    @Override\n    public String toString() {\n        return String.format(\"%s Shape (Color: %s)\", this.name, this.color);\n    }\n}\n\n/**\n * Circle implementation o...", "content_length": 1122, "segment_hash": "16663901..."}, {"file_path": "/home/<USER>/fengbin/DailyWork/ZeroAgents/competitor/Roo-Code/python_vector_search/examples/test_java_project/Shape.java", "identifier": null, "type": "fallback", "start_line": 108, "end_line": 157, "line_count": 50, "content_preview": "        return String.format(\"Circle (Radius: %.2f, Color: %s)\", this.radius, this.color);\n    }\n}\n\n/**\n * Rectangle implementation of Shape\n */\nclass Rectangle extends Shape {\n    \n    private double...", "content_length": 1116, "segment_hash": "f900e777..."}, {"file_path": "/home/<USER>/fengbin/DailyWork/ZeroAgents/competitor/Roo-Code/python_vector_search/examples/test_java_project/Shape.java", "identifier": null, "type": "fallback", "start_line": 158, "end_line": 180, "line_count": 23, "content_preview": "     * @param height the new height\n     */\n    public void setHeight(double height) {\n        this.height = height;\n    }\n    \n    @Override\n    public double calculateArea() {\n        return this.wi...", "content_length": 543, "segment_hash": "8e7670af..."}], "/home/<USER>/fengbin/DailyWork/ZeroAgents/competitor/Roo-Code/python_vector_search/examples/test_java_project/Calculator.java": [{"file_path": "/home/<USER>/fengbin/DailyWork/ZeroAgents/competitor/Roo-Code/python_vector_search/examples/test_java_project/Calculator.java", "identifier": null, "type": "fallback", "start_line": 1, "end_line": 40, "line_count": 40, "content_preview": "package com.example.calculator;\n\nimport java.util.ArrayList;\nimport java.util.List;\n\n/**\n * A simple calculator class that performs basic arithmetic operations\n * and maintains a history of calculatio...", "content_length": 1082, "segment_hash": "721f9fc0..."}, {"file_path": "/home/<USER>/fengbin/DailyWork/ZeroAgents/competitor/Roo-Code/python_vector_search/examples/test_java_project/Calculator.java", "identifier": null, "type": "fallback", "start_line": 41, "end_line": 72, "line_count": 32, "content_preview": "        addToHistory(String.format(\"%.2f + %.2f = %.2f\", this.currentValue - value, value, result));\n        return result;\n    }\n    \n    /**\n     * Subtract a number from the current value\n     * @p...", "content_length": 1110, "segment_hash": "5ccf455e..."}, {"file_path": "/home/<USER>/fengbin/DailyWork/ZeroAgents/competitor/Roo-Code/python_vector_search/examples/test_java_project/Calculator.java", "identifier": null, "type": "fallback", "start_line": 73, "end_line": 113, "line_count": 41, "content_preview": "     * @throws ArithmeticException if attempting to divide by zero\n     */\n    public double divide(double value) throws ArithmeticException {\n        if (value == 0) {\n            throw new Arithmeti...", "content_length": 1110, "segment_hash": "eba81569..."}, {"file_path": "/home/<USER>/fengbin/DailyWork/ZeroAgents/competitor/Roo-Code/python_vector_search/examples/test_java_project/Calculator.java", "identifier": null, "type": "fallback", "start_line": 114, "end_line": 146, "line_count": 33, "content_preview": "        addToHistory(\"History cleared\");\n    }\n    \n    /**\n     * Add an entry to the calculation history\n     * @param entry the history entry to add\n     */\n    private void addToHistory(String ent...", "content_length": 1074, "segment_hash": "b2b8074f..."}, {"file_path": "/home/<USER>/fengbin/DailyWork/ZeroAgents/competitor/Roo-Code/python_vector_search/examples/test_java_project/Calculator.java", "identifier": null, "type": "fallback", "start_line": 147, "end_line": 151, "line_count": 5, "content_preview": "        addToHistory(String.format(\"√%.2f = %.2f\", result * result, result));\n        return result;\n    }\n}\n", "content_length": 109, "segment_hash": "5b0e7c50..."}], "/home/<USER>/fengbin/DailyWork/ZeroAgents/competitor/Roo-Code/python_vector_search/examples/test_java_project/MathUtils.java": [{"file_path": "/home/<USER>/fengbin/DailyWork/ZeroAgents/competitor/Roo-Code/python_vector_search/examples/test_java_project/MathUtils.java", "identifier": null, "type": "fallback", "start_line": 1, "end_line": 43, "line_count": 43, "content_preview": "package com.example.utils;\n\n/**\n * Utility class containing static mathematical functions\n */\npublic class MathUtils {\n    \n    /**\n     * Private constructor to prevent instantiation\n     */\n    priv...", "content_length": 1135, "segment_hash": "ba0faa0e..."}, {"file_path": "/home/<USER>/fengbin/DailyWork/ZeroAgents/competitor/Roo-Code/python_vector_search/examples/test_java_project/MathUtils.java", "identifier": null, "type": "fallback", "start_line": 44, "end_line": 91, "line_count": 48, "content_preview": "        if (n <= 3) {\n            return true;\n        }\n        if (n % 2 == 0 || n % 3 == 0) {\n            return false;\n        }\n        \n        for (int i = 5; i * i <= n; i += 6) {\n            ...", "content_length": 1106, "segment_hash": "a539cec8..."}, {"file_path": "/home/<USER>/fengbin/DailyWork/ZeroAgents/competitor/Roo-Code/python_vector_search/examples/test_java_project/MathUtils.java", "identifier": null, "type": "fallback", "start_line": 92, "end_line": 133, "line_count": 42, "content_preview": "     * @param n the position in the <PERSON><PERSON><PERSON><PERSON> sequence\n     * @return the nth Fibonacci number\n     * @throws IllegalArgumentException if n is negative\n     */\n    public static long fibonacci(int n) ...", "content_length": 1141, "segment_hash": "30aa91f4..."}]}}
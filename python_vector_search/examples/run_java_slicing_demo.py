#!/usr/bin/env python3
"""
Java代码切片演示脚本
演示如何对Java代码库进行切片处理
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from java_code_slicer import JavaCodeSlicer


async def run_demo():
    """运行Java切片演示"""
    
    print("=" * 70)
    print("Java代码库切片演示")
    print("=" * 70)
    
    # 获取测试Java项目路径
    current_dir = Path(__file__).parent
    test_project_path = current_dir / "test_java_project"
    
    if not test_project_path.exists():
        print(f"错误: 测试项目路径不存在: {test_project_path}")
        return
    
    print(f"测试项目路径: {test_project_path}")
    print()
    
    # 创建切片器
    slicer = JavaCodeSlicer()
    
    try:
        # 执行切片
        print("开始切片处理...")
        slices = await slicer.slice_java_repository(str(test_project_path))
        
        # 显示摘要
        slicer.print_slice_summary(slices)
        
        # 显示详细信息
        slicer.print_detailed_slices(slices, max_files=10)
        
        # 分析切片结果
        print("\n" + "=" * 70)
        print("切片结果分析")
        print("=" * 70)
        
        analyze_slices(slices)
        
        # 保存结果
        output_file = current_dir / "java_slices_demo.json"
        save_slices_to_json(slices, slicer, str(output_file))
        
        print(f"\n✓ 演示完成! 结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


def analyze_slices(slices):
    """分析切片结果"""
    
    # 统计不同类型的代码块
    type_stats = {}
    identifier_stats = {}
    file_stats = {}
    
    for file_path, blocks in slices["files"].items():
        file_name = Path(file_path).name
        file_stats[file_name] = len(blocks)
        
        for block in blocks:
            # 统计类型
            block_type = block.type
            type_stats[block_type] = type_stats.get(block_type, 0) + 1
            
            # 统计标识符
            if block.identifier:
                identifier_stats[block.identifier] = identifier_stats.get(block.identifier, 0) + 1
    
    # 显示统计信息
    print("\n📊 代码块类型分布:")
    for block_type, count in sorted(type_stats.items(), key=lambda x: x[1], reverse=True):
        percentage = (count / slices["summary"]["total_blocks"]) * 100
        print(f"  {block_type}: {count} ({percentage:.1f}%)")
    
    print("\n📁 文件代码块分布:")
    for file_name, count in sorted(file_stats.items(), key=lambda x: x[1], reverse=True):
        print(f"  {file_name}: {count} 个代码块")
    
    print("\n🏷️  标识符统计 (前10个):")
    sorted_identifiers = sorted(identifier_stats.items(), key=lambda x: x[1], reverse=True)[:10]
    for identifier, count in sorted_identifiers:
        print(f"  {identifier}: {count} 次")
    
    # 分析代码块大小分布
    print("\n📏 代码块大小分析:")
    sizes = []
    for file_path, blocks in slices["files"].items():
        for block in blocks:
            line_count = block.end_line - block.start_line + 1
            sizes.append(line_count)
    
    if sizes:
        avg_size = sum(sizes) / len(sizes)
        min_size = min(sizes)
        max_size = max(sizes)
        
        print(f"  平均大小: {avg_size:.1f} 行")
        print(f"  最小大小: {min_size} 行")
        print(f"  最大大小: {max_size} 行")
        
        # 大小分布
        small_blocks = len([s for s in sizes if s <= 5])
        medium_blocks = len([s for s in sizes if 6 <= s <= 20])
        large_blocks = len([s for s in sizes if s > 20])
        
        print(f"  小块 (≤5行): {small_blocks}")
        print(f"  中块 (6-20行): {medium_blocks}")
        print(f"  大块 (>20行): {large_blocks}")


def save_slices_to_json(slices, slicer, output_file):
    """保存切片结果到JSON文件"""
    import json
    
    # 准备输出数据
    output_data = {
        "repository_path": slices["repository_path"],
        "summary": slices["summary"],
        "files": {}
    }
    
    # 转换CodeBlock对象为字典
    for file_path, blocks in slices["files"].items():
        output_data["files"][file_path] = [
            slicer.format_code_block(block) for block in blocks
        ]
    
    # 保存到文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)


def print_usage_examples():
    """打印使用示例"""
    print("\n" + "=" * 70)
    print("使用示例")
    print("=" * 70)
    
    print("\n1. 基本用法:")
    print("   python java_code_slicer.py /path/to/java/project")
    
    print("\n2. 保存结果到文件:")
    print("   python java_code_slicer.py /path/to/java/project --output slices.json")
    
    print("\n3. 显示详细信息:")
    print("   python java_code_slicer.py /path/to/java/project --detailed --max-files 5")
    
    print("\n4. 编程接口:")
    print("""
   from vector_search.parsers.tree_sitter_parser import TreeSitterParser
   
   parser = TreeSitterParser()
   blocks = await parser.parse_file("Calculator.java")
   
   for block in blocks:
       print(f"类型: {block.type}")
       print(f"标识符: {block.identifier}")
       print(f"位置: {block.start_line}-{block.end_line}")
   """)


if __name__ == "__main__":
    print("Java代码库切片演示")
    print("这个演示将展示如何将Java代码库切片为结构化的代码块")
    print()
    
    # 运行演示
    asyncio.run(run_demo())
    
    # 显示使用示例
    print_usage_examples()
    
    print("\n" + "=" * 70)
    print("演示结束")
    print("=" * 70)

package com.example.calculator;

import java.util.ArrayList;
import java.util.List;

/**
 * A simple calculator class that performs basic arithmetic operations
 * and maintains a history of calculations.
 */
public class Calculator {
    
    private double currentValue;
    private List<String> history;
    
    /**
     * Default constructor initializes calculator with zero value
     */
    public Calculator() {
        this.currentValue = 0.0;
        this.history = new ArrayList<>();
    }
    
    /**
     * Constructor with initial value
     * @param initialValue the starting value for calculations
     */
    public Calculator(double initialValue) {
        this.currentValue = initialValue;
        this.history = new ArrayList<>();
        addToHistory("Initialized with: " + initialValue);
    }
    
    /**
     * Add a number to the current value
     * @param value the number to add
     * @return the result of the addition
     */
    public double add(double value) {
        double result = this.currentValue + value;
        this.currentValue = result;
        addToHistory(String.format("%.2f + %.2f = %.2f", this.currentValue - value, value, result));
        return result;
    }
    
    /**
     * Subtract a number from the current value
     * @param value the number to subtract
     * @return the result of the subtraction
     */
    public double subtract(double value) {
        double result = this.currentValue - value;
        this.currentValue = result;
        addToHistory(String.format("%.2f - %.2f = %.2f", this.currentValue + value, value, result));
        return result;
    }
    
    /**
     * Multiply the current value by a number
     * @param value the number to multiply by
     * @return the result of the multiplication
     */
    public double multiply(double value) {
        double result = this.currentValue * value;
        this.currentValue = result;
        addToHistory(String.format("%.2f * %.2f = %.2f", this.currentValue / value, value, result));
        return result;
    }
    
    /**
     * Divide the current value by a number
     * @param value the number to divide by
     * @return the result of the division
     * @throws ArithmeticException if attempting to divide by zero
     */
    public double divide(double value) throws ArithmeticException {
        if (value == 0) {
            throw new ArithmeticException("Cannot divide by zero");
        }
        double result = this.currentValue / value;
        this.currentValue = result;
        addToHistory(String.format("%.2f / %.2f = %.2f", this.currentValue * value, value, result));
        return result;
    }
    
    /**
     * Get the current value
     * @return the current value
     */
    public double getCurrentValue() {
        return this.currentValue;
    }
    
    /**
     * Reset the calculator to zero
     */
    public void reset() {
        this.currentValue = 0.0;
        addToHistory("Calculator reset to 0");
    }
    
    /**
     * Get the calculation history
     * @return a copy of the calculation history
     */
    public List<String> getHistory() {
        return new ArrayList<>(this.history);
    }
    
    /**
     * Clear the calculation history
     */
    public void clearHistory() {
        this.history.clear();
        addToHistory("History cleared");
    }
    
    /**
     * Add an entry to the calculation history
     * @param entry the history entry to add
     */
    private void addToHistory(String entry) {
        this.history.add(entry);
    }
    
    /**
     * Calculate the square of the current value
     * @return the square of the current value
     */
    public double square() {
        double result = this.currentValue * this.currentValue;
        this.currentValue = result;
        addToHistory(String.format("%.2f² = %.2f", Math.sqrt(result), result));
        return result;
    }
    
    /**
     * Calculate the square root of the current value
     * @return the square root of the current value
     * @throws ArithmeticException if current value is negative
     */
    public double sqrt() throws ArithmeticException {
        if (this.currentValue < 0) {
            throw new ArithmeticException("Cannot calculate square root of negative number");
        }
        double result = Math.sqrt(this.currentValue);
        this.currentValue = result;
        addToHistory(String.format("√%.2f = %.2f", result * result, result));
        return result;
    }
}

package com.example.geometry;

/**
 * Abstract base class for geometric shapes
 */
public abstract class Shape {
    
    protected String name;
    protected String color;
    
    /**
     * Constructor for Shape
     * @param name the name of the shape
     * @param color the color of the shape
     */
    public Shape(String name, String color) {
        this.name = name;
        this.color = color;
    }
    
    /**
     * Abstract method to calculate area
     * @return the area of the shape
     */
    public abstract double calculateArea();
    
    /**
     * Abstract method to calculate perimeter
     * @return the perimeter of the shape
     */
    public abstract double calculatePerimeter();
    
    /**
     * Get the name of the shape
     * @return the shape name
     */
    public String getName() {
        return this.name;
    }
    
    /**
     * Get the color of the shape
     * @return the shape color
     */
    public String getColor() {
        return this.color;
    }
    
    /**
     * Set the color of the shape
     * @param color the new color
     */
    public void setColor(String color) {
        this.color = color;
    }
    
    @Override
    public String toString() {
        return String.format("%s Shape (Color: %s)", this.name, this.color);
    }
}

/**
 * Circle implementation of Shape
 */
class Circle extends Shape {
    
    private double radius;
    
    /**
     * Constructor for Circle
     * @param radius the radius of the circle
     * @param color the color of the circle
     */
    public Circle(double radius, String color) {
        super("Circle", color);
        this.radius = radius;
    }
    
    /**
     * Get the radius of the circle
     * @return the radius
     */
    public double getRadius() {
        return this.radius;
    }
    
    /**
     * Set the radius of the circle
     * @param radius the new radius
     */
    public void setRadius(double radius) {
        this.radius = radius;
    }
    
    @Override
    public double calculateArea() {
        return Math.PI * this.radius * this.radius;
    }
    
    @Override
    public double calculatePerimeter() {
        return 2 * Math.PI * this.radius;
    }
    
    @Override
    public String toString() {
        return String.format("Circle (Radius: %.2f, Color: %s)", this.radius, this.color);
    }
}

/**
 * Rectangle implementation of Shape
 */
class Rectangle extends Shape {
    
    private double width;
    private double height;
    
    /**
     * Constructor for Rectangle
     * @param width the width of the rectangle
     * @param height the height of the rectangle
     * @param color the color of the rectangle
     */
    public Rectangle(double width, double height, String color) {
        super("Rectangle", color);
        this.width = width;
        this.height = height;
    }
    
    /**
     * Get the width of the rectangle
     * @return the width
     */
    public double getWidth() {
        return this.width;
    }
    
    /**
     * Get the height of the rectangle
     * @return the height
     */
    public double getHeight() {
        return this.height;
    }
    
    /**
     * Set the width of the rectangle
     * @param width the new width
     */
    public void setWidth(double width) {
        this.width = width;
    }
    
    /**
     * Set the height of the rectangle
     * @param height the new height
     */
    public void setHeight(double height) {
        this.height = height;
    }
    
    @Override
    public double calculateArea() {
        return this.width * this.height;
    }
    
    @Override
    public double calculatePerimeter() {
        return 2 * (this.width + this.height);
    }
    
    @Override
    public String toString() {
        return String.format("Rectangle (Width: %.2f, Height: %.2f, Color: %s)", 
                           this.width, this.height, this.color);
    }
}

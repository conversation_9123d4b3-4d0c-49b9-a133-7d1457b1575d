#!/usr/bin/env python3
"""Advanced usage examples for the vector search package."""

import asyncio
import os
from pathlib import Path

from vector_search import VectorSearchEngine, Config
from vector_search.models import EmbedderProvider


async def ollama_example():
    """Example using Ollama embedder."""
    print("=== Ollama Embedder Example ===")
    
    config = Config(
        embedder_provider=EmbedderProvider.OLLAMA,
        ollama_base_url="http://localhost:11434",
        model_id="nomic-embed-text:latest",
        model_dimension=768,
        lancedb_path="./ollama_vector_db",
        search_min_score=0.2
    )
    
    engine = VectorSearchEngine(config)
    
    try:
        await engine.initialize()
        print("✓ Ollama embedder initialized")
        
        # Index some code
        example_dir = Path("./example_code")
        if example_dir.exists():
            summary = await engine.index_directory(str(example_dir))
            print(f"✓ Indexed {summary.indexed_blocks} blocks")
            
            # Perform search
            results = await engine.search("mathematical function")
            print(f"Found {len(results)} results")
        
    except Exception as e:
        print(f"Ollama example failed: {e}")
    finally:
        await engine.close()


async def gemini_example():
    """Example using Gemini embedder."""
    print("\n=== Gemini Embedder Example ===")
    
    config = Config(
        embedder_provider=EmbedderProvider.GEMINI,
        gemini_api_key=os.getenv("GEMINI_API_KEY"),
        lancedb_path="./gemini_vector_db",
        search_min_score=0.3
    )
    
    engine = VectorSearchEngine(config)
    
    try:
        await engine.initialize()
        print("✓ Gemini embedder initialized")
        
        # Index some code
        example_dir = Path("./example_code")
        if example_dir.exists():
            summary = await engine.index_directory(str(example_dir))
            print(f"✓ Indexed {summary.indexed_blocks} blocks")
            
            # Perform search
            results = await engine.search("class definition")
            print(f"Found {len(results)} results")
        
    except Exception as e:
        print(f"Gemini example failed: {e}")
    finally:
        await engine.close()


async def openai_compatible_example():
    """Example using OpenAI-compatible embedder."""
    print("\n=== OpenAI-Compatible Embedder Example ===")
    
    config = Config(
        embedder_provider=EmbedderProvider.OPENAI_COMPATIBLE,
        openai_compatible_base_url="https://api.your-provider.com/v1",
        openai_compatible_api_key=os.getenv("CUSTOM_API_KEY"),
        model_id="custom-embedding-model",
        model_dimension=1024,
        lancedb_path="./custom_vector_db"
    )
    
    engine = VectorSearchEngine(config)
    
    try:
        await engine.initialize()
        print("✓ OpenAI-compatible embedder initialized")
        
        # Index some code
        example_dir = Path("./example_code")
        if example_dir.exists():
            summary = await engine.index_directory(str(example_dir))
            print(f"✓ Indexed {summary.indexed_blocks} blocks")
        
    except Exception as e:
        print(f"OpenAI-compatible example failed: {e}")
    finally:
        await engine.close()


async def configuration_examples():
    """Examples of different configuration options."""
    print("\n=== Configuration Examples ===")
    
    # Example 1: Configuration from dictionary
    config_dict = {
        "embedder_provider": "openai",
        "openai_api_key": os.getenv("OPENAI_API_KEY"),
        "model_id": "text-embedding-3-large",
        "model_dimension": 3072,
        "lancedb_path": "./large_vector_db",
        "search_min_score": 0.4,
        "search_max_results": 100,
        "ignore_patterns": [
            ".git/**",
            "*.pyc",
            "__pycache__/**",
            "node_modules/**"
        ]
    }
    
    config = Config.from_dict(config_dict)
    print("✓ Created config from dictionary")
    
    # Example 2: Save and load configuration
    config_file = Path("./example_config.json")
    config.save_to_file(str(config_file))
    print(f"✓ Saved config to {config_file}")
    
    loaded_config = Config.from_file(str(config_file))
    print("✓ Loaded config from file")
    
    # Example 3: Custom ignore patterns
    custom_config = Config(
        embedder_provider=EmbedderProvider.OPENAI,
        openai_api_key=os.getenv("OPENAI_API_KEY"),
        model_id="text-embedding-3-small",
        model_dimension=1536,
        ignore_patterns=[
            # Standard patterns
            ".git/**",
            "node_modules/**",
            "__pycache__/**",
            "*.pyc",
            
            # Custom patterns for specific project
            "build/**",
            "dist/**",
            "coverage/**",
            "*.log",
            "*.tmp",
            "docs/build/**",
            "tests/fixtures/**",
            
            # Language-specific patterns
            "target/**",      # Rust
            "bin/**",         # Go
            "obj/**",         # C#
            ".next/**",       # Next.js
            ".nuxt/**",       # Nuxt.js
        ]
    )
    print("✓ Created config with custom ignore patterns")


async def search_examples():
    """Advanced search examples."""
    print("\n=== Advanced Search Examples ===")
    
    config = Config(
        embedder_provider=EmbedderProvider.OPENAI,
        openai_api_key=os.getenv("OPENAI_API_KEY"),
        model_id="text-embedding-3-small",
        model_dimension=1536,
        lancedb_path="./search_vector_db"
    )
    
    engine = VectorSearchEngine(config)
    
    try:
        await engine.initialize()
        
        # Ensure we have some indexed content
        example_dir = Path("./example_code")
        if example_dir.exists():
            await engine.index_directory(str(example_dir))
        
        # Example 1: Search with directory filtering
        print("\n1. Directory-filtered search:")
        results = await engine.search(
            "function definition",
            directory_prefix="example_code",
            max_results=5
        )
        for result in results:
            print(f"  - {result.file_path} (score: {result.score:.3f})")
        
        # Example 2: Search with file extension filtering
        print("\n2. File extension filtered search:")
        results = await engine.search(
            "class implementation",
            file_extensions=[".py"],
            max_results=5
        )
        for result in results:
            print(f"  - {result.file_path} (score: {result.score:.3f})")
        
        # Example 3: Search with custom scoring threshold
        print("\n3. High-confidence search (min_score=0.7):")
        results = await engine.search(
            "mathematical calculation",
            min_score=0.7,
            max_results=3
        )
        for result in results:
            print(f"  - {result.file_path} (score: {result.score:.3f})")
        
        # Example 4: Search for specific patterns
        search_queries = [
            "error handling try catch",
            "async await function",
            "database connection",
            "unit test assertion",
            "configuration settings",
            "logging debug info"
        ]
        
        print("\n4. Pattern-based searches:")
        for query in search_queries:
            results = await engine.search(query, max_results=2)
            print(f"  '{query}': {len(results)} results")
        
        # Example 5: Similar code search
        print("\n5. Similar code search:")
        code_snippets = [
            "def calculate(a, b): return a + b",
            "function process(data) { return data.map(x => x * 2); }",
            "class Helper { constructor() { this.data = []; } }"
        ]
        
        for snippet in code_snippets:
            results = await engine.search_similar_code(snippet, max_results=2)
            print(f"  Similar to '{snippet[:30]}...': {len(results)} results")
    
    except Exception as e:
        print(f"Search examples failed: {e}")
    finally:
        await engine.close()


async def batch_processing_example():
    """Example of batch processing with progress tracking."""
    print("\n=== Batch Processing Example ===")
    
    config = Config(
        embedder_provider=EmbedderProvider.OPENAI,
        openai_api_key=os.getenv("OPENAI_API_KEY"),
        model_id="text-embedding-3-small",
        model_dimension=1536,
        lancedb_path="./batch_vector_db",
        batch_segment_threshold=50,  # Process in smaller batches
        parsing_concurrency=5        # Limit concurrent parsing
    )
    
    engine = VectorSearchEngine(config)
    
    try:
        await engine.initialize()
        
        # Create a larger example directory
        large_dir = Path("./large_example")
        large_dir.mkdir(exist_ok=True)
        
        # Create multiple files
        for i in range(10):
            file_path = large_dir / f"module_{i}.py"
            file_path.write_text(f'''
"""Module {i} - Example code for batch processing."""

def function_{i}_a(x):
    """Function {i}a - processes input x."""
    return x * {i}

def function_{i}_b(y):
    """Function {i}b - processes input y."""
    return y + {i}

class Class{i}:
    """Class {i} - example class."""
    
    def __init__(self):
        self.value = {i}
    
    def method_{i}(self, data):
        """Method {i} - processes data."""
        return data ** {i}
''')
        
        # Progress tracking
        total_files_processed = 0
        total_blocks_processed = 0
        
        def file_progress(processed, total, current_file):
            nonlocal total_files_processed
            total_files_processed = processed
            print(f"Files: {processed}/{total} - {current_file}")
        
        def block_progress(blocks):
            nonlocal total_blocks_processed
            total_blocks_processed = blocks
            if blocks % 20 == 0:
                print(f"Blocks processed: {blocks}")
        
        # Index with progress tracking
        summary = await engine.index_directory(
            str(large_dir),
            progress_callback=file_progress,
            block_callback=block_progress
        )
        
        print(f"\n✓ Batch processing complete:")
        print(f"  Files processed: {summary.processed_files}")
        print(f"  Blocks indexed: {summary.indexed_blocks}")
        print(f"  Processing time: {summary.processing_time:.2f}s")
        print(f"  Average time per file: {summary.processing_time/summary.processed_files:.2f}s")
        
        # Test search on the indexed content
        results = await engine.search("function processes input", max_results=5)
        print(f"\nSearch results: {len(results)} found")
        
    except Exception as e:
        print(f"Batch processing example failed: {e}")
    finally:
        await engine.close()


async def main():
    """Run all advanced examples."""
    print("Vector Search - Advanced Usage Examples")
    print("=" * 50)
    
    # Run configuration examples first
    await configuration_examples()
    
    # Run embedder examples (these might fail if services aren't available)
    await ollama_example()
    await gemini_example()
    await openai_compatible_example()
    
    # Run search examples
    await search_examples()
    
    # Run batch processing example
    await batch_processing_example()
    
    print("\n✓ All advanced examples completed!")


if __name__ == "__main__":
    asyncio.run(main())

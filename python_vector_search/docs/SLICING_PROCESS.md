# 代码切片流程详解

本文档详细说明了向量搜索包中的代码切片流程，以及如何将代码库分解为可搜索的代码块。

## 切片流程概述

```
输入: 代码库目录
  ↓
1. 文件发现 (File Discovery)
  ↓
2. 文件过滤 (File Filtering) 
  ↓
3. 内容读取 (Content Reading)
  ↓
4. 语言识别 (Language Detection)
  ↓
5. 语法解析 (Syntax Parsing)
  ↓
6. AST遍历 (AST Traversal)
  ↓
7. 代码块提取 (Code Block Extraction)
  ↓
8. 智能分块 (Intelligent Chunking)
  ↓
9. 元数据生成 (Metadata Generation)
  ↓
10. 哈希计算 (Hash Calculation)
  ↓
输出: 结构化代码块列表
```

## 详细流程说明

### 1. 文件发现 (File Discovery)

**位置**: `scanner.py` → `_find_supported_files()`

```python
def _find_supported_files(self, directory_path: Path) -> List[str]:
    """递归扫描目录，查找所有支持的文件"""
    for file_path in directory_path.rglob('*'):
        if file_path.is_file():
            # 检查文件扩展名
            if file_path.suffix.lower() in SUPPORTED_EXTENSIONS:
                yield str(file_path)
```

**支持的Java相关扩展名**:
- `.java` - Java源文件
- `.kt`, `.kts` - Kotlin文件 (JVM语言)
- `.scala` - Scala文件 (JVM语言)

### 2. 文件过滤 (File Filtering)

**过滤条件**:
- 文件大小限制 (默认1MB)
- 忽略模式匹配 (gitignore风格)
- 文件扩展名检查

**常见Java项目忽略模式**:
```python
ignore_patterns = [
    "target/**",        # Maven构建目录
    "build/**",         # Gradle构建目录
    ".gradle/**",       # Gradle缓存
    "*.class",          # 编译后的字节码
    "*.jar",            # JAR包
    "*.war",            # WAR包
    ".idea/**",         # IntelliJ IDEA配置
    ".eclipse/**",      # Eclipse配置
]
```

### 3. 内容读取 (Content Reading)

```python
async def parse_file(self, file_path: str, content: Optional[str] = None):
    if content is None:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
```

**编码处理**:
- 优先使用UTF-8编码
- 自动检测文件编码
- 错误处理和降级策略

### 4. 语言识别 (Language Detection)

**位置**: `tree_sitter_parser.py` → `is_supported_language()`

```python
LANGUAGE_MAPPINGS = {
    ".java": "java",
    ".kt": "kotlin", 
    ".kts": "kotlin",
    ".scala": "scala"
}

def is_supported_language(self, file_path: str) -> bool:
    extension = self.get_file_extension(file_path)
    return extension in LANGUAGE_MAPPINGS
```

### 5. 语法解析 (Syntax Parsing)

**Tree-sitter解析流程**:

```python
async def _parse_content(self, file_path: str, content: str, file_hash: str):
    # 获取语言解析器
    language_name = LANGUAGE_MAPPINGS.get(extension)
    parser = self.parsers[language_name]
    
    # 解析生成AST
    tree = parser.parse(content.encode('utf-8'))
    
    # 提取代码块
    return self._extract_blocks_from_ast(file_path, content, file_hash, tree.root_node)
```

### 6. AST遍历 (AST Traversal)

**Java语言的关键节点类型**:

```python
JAVA_INTERESTING_NODES = {
    'class_declaration',           # 类声明
    'interface_declaration',       # 接口声明
    'enum_declaration',           # 枚举声明
    'method_declaration',         # 方法声明
    'constructor_declaration',    # 构造函数声明
    'field_declaration',          # 字段声明
    'annotation_type_declaration', # 注解声明
    'import_declaration',         # 导入声明
    'package_declaration'         # 包声明
}
```

**遍历算法**:
```python
def _extract_blocks_from_ast(self, file_path, content, file_hash, root_node):
    results = []
    
    def traverse(node):
        if self._is_interesting_node(node):
            # 提取代码块
            block = self._create_code_block(node, content, file_path, file_hash)
            if block:
                results.append(block)
        
        # 递归遍历子节点
        for child in node.children:
            traverse(child)
    
    traverse(root_node)
    return results
```

### 7. 代码块提取 (Code Block Extraction)

**Java代码块示例**:

```java
// 输入Java代码
public class Calculator {
    private int value;
    
    public Calculator(int initialValue) {
        this.value = initialValue;
    }
    
    public int add(int number) {
        return this.value + number;
    }
}
```

**提取的代码块**:
1. **类声明块**:
   - 类型: `class_declaration`
   - 标识符: `Calculator`
   - 内容: 整个类定义
   - 行数: 1-9

2. **字段声明块**:
   - 类型: `field_declaration`
   - 标识符: `value`
   - 内容: `private int value;`
   - 行数: 2-2

3. **构造函数块**:
   - 类型: `constructor_declaration`
   - 标识符: `Calculator`
   - 内容: 构造函数定义
   - 行数: 4-6

4. **方法声明块**:
   - 类型: `method_declaration`
   - 标识符: `add`
   - 内容: 方法定义
   - 行数: 8-10

### 8. 智能分块 (Intelligent Chunking)

**分块策略**:

```python
# 配置参数
MAX_BLOCK_CHARS = 1000          # 最大块大小
MIN_BLOCK_CHARS = 50            # 最小块大小
MIN_CHUNK_REMAINDER_CHARS = 200 # 最小剩余块大小
```

**分块算法**:
1. **小块合并**: 小于MIN_BLOCK_CHARS的块与相邻块合并
2. **大块拆分**: 超过MAX_BLOCK_CHARS的块按行拆分
3. **智能边界**: 在语法边界处拆分，避免破坏代码结构
4. **剩余处理**: 避免产生过小的剩余块

**Java大类拆分示例**:
```java
// 原始大类 (>1000字符)
public class LargeService {
    // 方法1 (300字符)
    public void method1() { ... }
    
    // 方法2 (400字符) 
    public void method2() { ... }
    
    // 方法3 (500字符)
    public void method3() { ... }
}

// 拆分结果:
// 块1: 类声明 + 方法1 (600字符)
// 块2: 方法2 (400字符)  
// 块3: 方法3 (500字符)
```

### 9. 元数据生成 (Metadata Generation)

**CodeBlock数据结构**:
```python
@dataclass
class CodeBlock:
    file_path: str          # 文件路径
    identifier: str         # 标识符 (类名/方法名)
    type: str              # 节点类型
    start_line: int        # 起始行号
    end_line: int          # 结束行号
    content: str           # 代码内容
    segment_hash: str      # 段落哈希
    file_hash: str         # 文件哈希
```

**Java元数据提取**:
```python
def _extract_java_metadata(self, node, content):
    metadata = {}
    
    # 提取类名
    if node.type == 'class_declaration':
        class_name = self._find_identifier(node, content)
        metadata['class_name'] = class_name
    
    # 提取方法签名
    elif node.type == 'method_declaration':
        method_name = self._find_identifier(node, content)
        parameters = self._extract_parameters(node, content)
        metadata['method_name'] = method_name
        metadata['parameters'] = parameters
    
    return metadata
```

### 10. 哈希计算 (Hash Calculation)

**段落哈希算法**:
```python
def create_segment_hash(self, file_path, start_line, end_line, content):
    content_preview = content[:100]  # 内容预览
    hash_input = f"{file_path}-{start_line}-{end_line}-{len(content)}-{content_preview}"
    return hashlib.sha256(hash_input.encode()).hexdigest()
```

**用途**:
- 去重检测
- 增量更新
- 缓存管理
- 版本控制

## 使用示例

### 基本用法

```bash
# 切片Java代码库
python examples/java_code_slicer.py /path/to/java/project

# 保存结果到JSON文件
python examples/java_code_slicer.py /path/to/java/project --output slices.json

# 显示详细信息
python examples/java_code_slicer.py /path/to/java/project --detailed --max-files 5
```

### 编程接口

```python
from vector_search.parsers.tree_sitter_parser import TreeSitterParser

# 创建解析器
parser = TreeSitterParser()

# 解析单个文件
blocks = await parser.parse_file("Calculator.java")

# 遍历代码块
for block in blocks:
    print(f"类型: {block.type}")
    print(f"标识符: {block.identifier}")
    print(f"位置: {block.start_line}-{block.end_line}")
    print(f"内容: {block.content[:100]}...")
```

## 输出格式

### JSON输出结构

```json
{
  "repository_path": "/path/to/java/project",
  "summary": {
    "total_files": 25,
    "processed_files": 23,
    "total_blocks": 156,
    "success_rate": "92.0%"
  },
  "files": {
    "src/main/java/Calculator.java": [
      {
        "file_path": "src/main/java/Calculator.java",
        "identifier": "Calculator",
        "type": "class_declaration",
        "start_line": 1,
        "end_line": 20,
        "line_count": 20,
        "content_preview": "public class Calculator {\n    private int value;\n    \n    public Calculator(int initialValue) {\n        this.value = initialValue;\n    }...",
        "content_length": 456,
        "segment_hash": "a1b2c3d4..."
      }
    ]
  }
}
```

## 性能优化

### 并发处理
- 文件级别并发解析
- 批量处理优化
- 内存使用控制

### 缓存策略
- 文件哈希缓存
- 增量更新
- 智能跳过未更改文件

### 错误处理
- 单文件失败不影响整体
- 详细错误报告
- 优雅降级策略

这个切片流程确保了代码的结构化提取，为后续的向量化和搜索提供了高质量的输入数据。

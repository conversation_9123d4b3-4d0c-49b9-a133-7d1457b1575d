"""Tests for code parsers."""

import pytest
from pathlib import Path

from vector_search.parsers.tree_sitter_parser import Tree<PERSON>itterParser
from vector_search.parsers.markdown_parser import MarkdownParser


class TestTreeSitterParser:
    """Test tree-sitter parser functionality."""
    
    @pytest.fixture
    def parser(self):
        """Create parser instance."""
        return TreeSitterParser()
    
    def test_is_supported_language(self, parser):
        """Test language support detection."""
        assert parser.is_supported_language("test.py")
        assert parser.is_supported_language("test.js")
        assert parser.is_supported_language("test.ts")
        assert parser.is_supported_language("test.rs")
        assert parser.is_supported_language("test.go")
        assert parser.is_supported_language("test.java")
        assert parser.is_supported_language("test.cpp")
        assert parser.is_supported_language("test.c")
        assert parser.is_supported_language("test.cs")
        
        assert not parser.is_supported_language("test.txt")
        assert not parser.is_supported_language("test.pdf")
        assert not parser.is_supported_language("test.exe")
    
    def test_get_file_extension(self, parser):
        """Test file extension extraction."""
        assert parser.get_file_extension("test.py") == ".py"
        assert parser.get_file_extension("test.JS") == ".js"
        assert parser.get_file_extension("/path/to/file.cpp") == ".cpp"
        assert parser.get_file_extension("file") == ""
    
    def test_create_file_hash(self, parser):
        """Test file hash creation."""
        content1 = "def hello(): pass"
        content2 = "def hello(): pass"
        content3 = "def goodbye(): pass"
        
        hash1 = parser.create_file_hash(content1)
        hash2 = parser.create_file_hash(content2)
        hash3 = parser.create_file_hash(content3)
        
        assert hash1 == hash2  # Same content should have same hash
        assert hash1 != hash3  # Different content should have different hash
        assert len(hash1) == 64  # SHA256 hash length
    
    @pytest.mark.asyncio
    async def test_parse_python_content(self, parser, sample_python_code, temp_dir):
        """Test parsing Python code."""
        # Create temporary Python file
        python_file = Path(temp_dir) / "test.py"
        python_file.write_text(sample_python_code)
        
        blocks = await parser.parse_file(str(python_file))
        
        # Should find some blocks (exact number depends on implementation)
        assert len(blocks) >= 0
        
        # Check that blocks have required fields
        for block in blocks:
            assert block.file_path == str(python_file)
            assert block.content
            assert block.start_line > 0
            assert block.end_line >= block.start_line
            assert block.segment_hash
            assert block.file_hash
    
    @pytest.mark.asyncio
    async def test_parse_with_content_parameter(self, parser, sample_python_code):
        """Test parsing with content parameter."""
        blocks = await parser.parse_file(
            "test.py", 
            content=sample_python_code,
            file_hash="test-hash"
        )
        
        # Should parse content without reading file
        assert len(blocks) >= 0
        
        for block in blocks:
            assert block.file_path == "test.py"
            assert block.file_hash == "test-hash"
    
    @pytest.mark.asyncio
    async def test_parse_unsupported_file(self, parser):
        """Test parsing unsupported file type."""
        blocks = await parser.parse_file("test.txt", content="Some text content")
        
        # Should return empty list for unsupported files
        assert blocks == []


class TestMarkdownParser:
    """Test markdown parser functionality."""
    
    @pytest.fixture
    def parser(self):
        """Create markdown parser instance."""
        return MarkdownParser()
    
    def test_is_supported_language(self, parser):
        """Test markdown file support detection."""
        assert parser.is_supported_language("README.md")
        assert parser.is_supported_language("docs.markdown")
        assert parser.is_supported_language("test.MD")
        
        assert not parser.is_supported_language("test.py")
        assert not parser.is_supported_language("test.txt")
    
    @pytest.mark.asyncio
    async def test_parse_markdown_content(self, parser, sample_markdown_content, temp_dir):
        """Test parsing markdown content."""
        # Create temporary markdown file
        md_file = Path(temp_dir) / "test.md"
        md_file.write_text(sample_markdown_content)
        
        blocks = await parser.parse_file(str(md_file))
        
        # Should find some blocks
        assert len(blocks) > 0
        
        # Check for different block types
        block_types = {block.type for block in blocks}
        
        # Should have sections and code blocks
        assert any("section" in bt for bt in block_types)
        assert any("code_block" in bt for bt in block_types)
        
        # Check that blocks have required fields
        for block in blocks:
            assert block.file_path == str(md_file)
            assert block.content
            assert block.start_line > 0
            assert block.end_line >= block.start_line
            assert block.segment_hash
            assert block.file_hash
    
    @pytest.mark.asyncio
    async def test_parse_markdown_headers(self, parser):
        """Test parsing markdown headers."""
        content = """
# Main Title

This is the main section content.

## Subsection

This is subsection content.

### Sub-subsection

More content here.
"""
        
        blocks = await parser.parse_file("test.md", content=content)
        
        # Should find header sections
        header_blocks = [b for b in blocks if "section" in b.type]
        assert len(header_blocks) > 0
        
        # Check that identifiers are extracted from headers
        identifiers = [b.identifier for b in header_blocks if b.identifier]
        assert "Main Title" in identifiers
        assert "Subsection" in identifiers
        assert "Sub-subsection" in identifiers
    
    @pytest.mark.asyncio
    async def test_parse_markdown_code_blocks(self, parser):
        """Test parsing markdown code blocks."""
        content = """
# Code Examples

Here's a Python example:

```python
def hello():
    print("Hello, world!")
```

And a JavaScript example:

```javascript
function greet() {
    console.log("Hello!");
}
```

Some text without code.
"""
        
        blocks = await parser.parse_file("test.md", content=content)
        
        # Should find code blocks
        code_blocks = [b for b in blocks if "code_block" in b.type]
        assert len(code_blocks) >= 2
        
        # Check code block content
        python_blocks = [b for b in code_blocks if "python" in b.type]
        js_blocks = [b for b in code_blocks if "javascript" in b.type]
        
        assert len(python_blocks) >= 1
        assert len(js_blocks) >= 1
        
        # Check that code content is extracted correctly
        python_block = python_blocks[0]
        assert "def hello():" in python_block.content
        assert "print" in python_block.content
    
    @pytest.mark.asyncio
    async def test_parse_empty_markdown(self, parser):
        """Test parsing empty markdown file."""
        blocks = await parser.parse_file("empty.md", content="")
        
        # Should return empty list for empty content
        assert blocks == []
    
    @pytest.mark.asyncio
    async def test_parse_markdown_without_code_blocks(self, parser):
        """Test parsing markdown without code blocks."""
        content = """
# Simple Document

This is just text content without any code blocks.

## Another Section

More text here.
"""
        
        blocks = await parser.parse_file("simple.md", content=content)
        
        # Should find section blocks but no code blocks
        section_blocks = [b for b in blocks if "section" in b.type]
        code_blocks = [b for b in blocks if "code_block" in b.type]
        
        assert len(section_blocks) > 0
        assert len(code_blocks) == 0

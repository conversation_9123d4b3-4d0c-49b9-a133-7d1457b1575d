"""Tests for cache management."""

import pytest
import json
from pathlib import Path

from vector_search.cache import CacheManager


class TestCacheManager:
    """Test cache manager functionality."""
    
    @pytest.mark.asyncio
    async def test_cache_initialization(self, temp_dir):
        """Test cache manager initialization."""
        cache_path = str(Path(temp_dir) / "cache.json")
        
        async with CacheManager(cache_path, temp_dir) as cache:
            assert cache.cache_path == Path(cache_path)
            assert cache.workspace_path == temp_dir
            assert cache.file_hashes == {}
    
    @pytest.mark.asyncio
    async def test_cache_hash_operations(self, temp_dir):
        """Test basic hash operations."""
        cache_path = str(Path(temp_dir) / "cache.json")
        
        async with CacheManager(cache_path, temp_dir) as cache:
            # Test getting non-existent hash
            assert cache.get_hash("nonexistent.py") is None
            
            # Test updating hash
            cache.update_hash("test.py", "hash123")
            assert cache.get_hash("test.py") == "hash123"
            
            # Test updating existing hash
            cache.update_hash("test.py", "hash456")
            assert cache.get_hash("test.py") == "hash456"
            
            # Test deleting hash
            cache.delete_hash("test.py")
            assert cache.get_hash("test.py") is None
    
    @pytest.mark.asyncio
    async def test_cache_persistence(self, temp_dir):
        """Test cache persistence across sessions."""
        cache_path = str(Path(temp_dir) / "cache.json")
        
        # First session - add some data
        async with CacheManager(cache_path, temp_dir) as cache:
            cache.update_hash("file1.py", "hash1")
            cache.update_hash("file2.js", "hash2")
        
        # Second session - data should be loaded
        async with CacheManager(cache_path, temp_dir) as cache:
            assert cache.get_hash("file1.py") == "hash1"
            assert cache.get_hash("file2.js") == "hash2"
    
    @pytest.mark.asyncio
    async def test_cache_file_changed_detection(self, temp_dir):
        """Test file change detection."""
        cache_path = str(Path(temp_dir) / "cache.json")
        
        async with CacheManager(cache_path, temp_dir) as cache:
            # New file should be detected as changed
            assert cache.has_file_changed("new_file.py", "hash1") is True
            
            # Add file to cache
            cache.update_hash("new_file.py", "hash1")
            
            # Same hash should not be detected as changed
            assert cache.has_file_changed("new_file.py", "hash1") is False
            
            # Different hash should be detected as changed
            assert cache.has_file_changed("new_file.py", "hash2") is True
    
    @pytest.mark.asyncio
    async def test_cache_get_changed_files(self, temp_dir):
        """Test getting changed files."""
        cache_path = str(Path(temp_dir) / "cache.json")
        
        async with CacheManager(cache_path, temp_dir) as cache:
            # Add some files to cache
            cache.update_hash("file1.py", "hash1")
            cache.update_hash("file2.py", "hash2")
            cache.update_hash("file3.py", "hash3")
            
            # Test with current file hashes
            current_hashes = {
                "file1.py": "hash1",      # Unchanged
                "file2.py": "new_hash2",  # Changed
                "file3.py": "hash3",      # Unchanged
                "file4.py": "hash4",      # New file
            }
            
            changed_files = cache.get_changed_files(current_hashes)
            
            assert "file1.py" not in changed_files  # Unchanged
            assert "file2.py" in changed_files      # Changed
            assert "file3.py" not in changed_files  # Unchanged
            assert "file4.py" in changed_files      # New file
    
    @pytest.mark.asyncio
    async def test_cache_get_deleted_files(self, temp_dir):
        """Test getting deleted files."""
        cache_path = str(Path(temp_dir) / "cache.json")
        
        async with CacheManager(cache_path, temp_dir) as cache:
            # Add some files to cache
            cache.update_hash("file1.py", "hash1")
            cache.update_hash("file2.py", "hash2")
            cache.update_hash("file3.py", "hash3")
            cache.update_hash("file4.py", "hash4")
            
            # Current files (file2.py and file4.py are deleted)
            current_files = {"file1.py", "file3.py", "file5.py"}
            
            deleted_files = cache.get_deleted_files(current_files)
            
            assert "file1.py" not in deleted_files  # Still exists
            assert "file2.py" in deleted_files      # Deleted
            assert "file3.py" not in deleted_files  # Still exists
            assert "file4.py" in deleted_files      # Deleted
            assert "file5.py" not in deleted_files  # New file, not in cache
    
    @pytest.mark.asyncio
    async def test_cache_multiple_operations(self, temp_dir):
        """Test multiple hash operations."""
        cache_path = str(Path(temp_dir) / "cache.json")
        
        async with CacheManager(cache_path, temp_dir) as cache:
            # Test updating multiple hashes
            file_hashes = {
                "file1.py": "hash1",
                "file2.py": "hash2",
                "file3.py": "hash3",
            }
            
            cache.update_multiple_hashes(file_hashes)
            
            for file_path, expected_hash in file_hashes.items():
                assert cache.get_hash(file_path) == expected_hash
            
            # Test deleting multiple hashes
            files_to_delete = {"file1.py", "file3.py"}
            cache.delete_multiple_hashes(files_to_delete)
            
            assert cache.get_hash("file1.py") is None
            assert cache.get_hash("file2.py") == "hash2"  # Should remain
            assert cache.get_hash("file3.py") is None
    
    @pytest.mark.asyncio
    async def test_cache_get_all_hashes(self, temp_dir):
        """Test getting all hashes."""
        cache_path = str(Path(temp_dir) / "cache.json")
        
        async with CacheManager(cache_path, temp_dir) as cache:
            # Add some hashes
            cache.update_hash("file1.py", "hash1")
            cache.update_hash("file2.py", "hash2")
            
            all_hashes = cache.get_all_hashes()
            
            assert all_hashes == {
                "file1.py": "hash1",
                "file2.py": "hash2"
            }
            
            # Ensure it's a copy (modifying shouldn't affect cache)
            all_hashes["file3.py"] = "hash3"
            assert cache.get_hash("file3.py") is None
    
    @pytest.mark.asyncio
    async def test_cache_clear(self, temp_dir):
        """Test cache clearing."""
        cache_path = str(Path(temp_dir) / "cache.json")
        
        async with CacheManager(cache_path, temp_dir) as cache:
            # Add some data
            cache.update_hash("file1.py", "hash1")
            cache.update_hash("file2.py", "hash2")
            
            # Clear cache
            await cache.clear()
            
            assert cache.file_hashes == {}
            assert not Path(cache_path).exists()
    
    def test_create_file_hash(self):
        """Test file hash creation."""
        content1 = "def hello(): pass"
        content2 = "def hello(): pass"
        content3 = "def goodbye(): pass"
        
        hash1 = CacheManager.create_file_hash(content1)
        hash2 = CacheManager.create_file_hash(content2)
        hash3 = CacheManager.create_file_hash(content3)
        
        assert hash1 == hash2  # Same content should have same hash
        assert hash1 != hash3  # Different content should have different hash
        assert len(hash1) == 64  # SHA256 hash length
    
    @pytest.mark.asyncio
    async def test_create_file_hash_from_path(self, temp_dir):
        """Test creating hash from file path."""
        # Create test file
        test_file = Path(temp_dir) / "test.py"
        content = "def test(): return 42"
        test_file.write_text(content)
        
        # Create hash from file
        file_hash = await CacheManager.create_file_hash_from_path(str(test_file))
        
        # Should match hash of content
        expected_hash = CacheManager.create_file_hash(content)
        assert file_hash == expected_hash
    
    @pytest.mark.asyncio
    async def test_create_file_hash_from_nonexistent_path(self):
        """Test creating hash from non-existent file path."""
        # Should not raise exception, but return a unique hash
        file_hash = await CacheManager.create_file_hash_from_path("nonexistent.py")
        
        assert isinstance(file_hash, str)
        assert len(file_hash) == 64  # SHA256 hash length
    
    @pytest.mark.asyncio
    async def test_cache_corrupted_file(self, temp_dir):
        """Test handling corrupted cache file."""
        cache_path = Path(temp_dir) / "cache.json"
        
        # Create corrupted cache file
        cache_path.write_text("invalid json content")
        
        # Should handle gracefully and start with empty cache
        async with CacheManager(str(cache_path), temp_dir) as cache:
            assert cache.file_hashes == {}

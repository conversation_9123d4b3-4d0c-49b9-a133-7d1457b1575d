"""Integration tests for the vector search engine."""

import pytest
from unittest.mock import Async<PERSON>ock, MagicMock

from vector_search.engine import VectorSearchEngine
from vector_search.config import Config
from vector_search.models import EmbedderProvider


class MockEmbedder:
    """Mock embedder for testing."""
    
    def __init__(self):
        self.embedder_info = {"name": "mock"}
    
    async def validate_configuration(self):
        return {"valid": True}
    
    async def create_embeddings(self, texts, model=None):
        # Return mock embeddings (same dimension for all)
        embeddings = [[0.1] * 1536 for _ in texts]
        return type('EmbeddingResponse', (), {
            'embeddings': embeddings,
            'usage': {'prompt_tokens': len(texts) * 10, 'total_tokens': len(texts) * 10}
        })()


class TestVectorSearchEngineIntegration:
    """Integration tests for the vector search engine."""
    
    @pytest.fixture
    def mock_config(self, temp_dir):
        """Create mock configuration."""
        return Config(
            embedder_provider=EmbedderProvider.OPENAI,
            openai_api_key="test-key",
            model_id="text-embedding-3-small",
            model_dimension=1536,
            lancedb_path=str(temp_dir) + "/test_db",
            cache_path=str(temp_dir) + "/cache.json",
            search_min_score=0.1,
            search_max_results=10
        )
    
    @pytest.mark.asyncio
    async def test_engine_initialization(self, mock_config, monkeypatch):
        """Test engine initialization."""
        # Mock the embedder creation
        def mock_create_embedder(self):
            return MockEmbedder()
        
        monkeypatch.setattr(VectorSearchEngine, '_create_embedder', mock_create_embedder)
        
        engine = VectorSearchEngine(mock_config)
        
        # Should not be initialized yet
        assert not engine._initialized
        
        # Initialize
        collection_created = await engine.initialize()
        
        # Should be initialized now
        assert engine._initialized
        assert isinstance(collection_created, bool)
        assert engine.embedder is not None
        assert engine.vector_store is not None
        assert engine.parser is not None
        assert engine.cache_manager is not None
        assert engine.scanner is not None
        assert engine.search_service is not None
    
    @pytest.mark.asyncio
    async def test_engine_index_directory(self, mock_config, create_test_files, monkeypatch):
        """Test indexing a directory."""
        # Mock the embedder creation
        def mock_create_embedder(self):
            return MockEmbedder()
        
        monkeypatch.setattr(VectorSearchEngine, '_create_embedder', mock_create_embedder)
        
        engine = VectorSearchEngine(mock_config)
        test_dir = create_test_files
        
        # Index the directory
        summary = await engine.index_directory(str(test_dir))
        
        # Check summary
        assert summary.total_files > 0
        assert summary.processed_files >= 0
        assert summary.processing_time > 0
        assert isinstance(summary.errors, list)
    
    @pytest.mark.asyncio
    async def test_engine_index_single_file(self, mock_config, temp_dir, sample_python_code, monkeypatch):
        """Test indexing a single file."""
        # Mock the embedder creation
        def mock_create_embedder(self):
            return MockEmbedder()
        
        monkeypatch.setattr(VectorSearchEngine, '_create_embedder', mock_create_embedder)
        
        engine = VectorSearchEngine(mock_config)
        
        # Create test file
        from pathlib import Path
        test_file = Path(temp_dir) / "test.py"
        test_file.write_text(sample_python_code)
        
        # Index the file
        success = await engine.index_file(str(test_file))
        
        # Should succeed (even if no actual indexing happens due to mocking)
        assert isinstance(success, bool)
    
    @pytest.mark.asyncio
    async def test_engine_search(self, mock_config, monkeypatch):
        """Test search functionality."""
        # Mock the embedder creation
        def mock_create_embedder(self):
            return MockEmbedder()
        
        monkeypatch.setattr(VectorSearchEngine, '_create_embedder', mock_create_embedder)
        
        engine = VectorSearchEngine(mock_config)
        await engine.initialize()
        
        # Perform search
        results = await engine.search("test query")
        
        # Should return a list (even if empty due to no indexed data)
        assert isinstance(results, list)
    
    @pytest.mark.asyncio
    async def test_engine_search_similar_code(self, mock_config, monkeypatch):
        """Test similar code search."""
        # Mock the embedder creation
        def mock_create_embedder(self):
            return MockEmbedder()
        
        monkeypatch.setattr(VectorSearchEngine, '_create_embedder', mock_create_embedder)
        
        engine = VectorSearchEngine(mock_config)
        await engine.initialize()
        
        # Search for similar code
        results = await engine.search_similar_code("def test(): pass")
        
        # Should return a list
        assert isinstance(results, list)
    
    @pytest.mark.asyncio
    async def test_engine_search_by_function_name(self, mock_config, monkeypatch):
        """Test function name search."""
        # Mock the embedder creation
        def mock_create_embedder(self):
            return MockEmbedder()
        
        monkeypatch.setattr(VectorSearchEngine, '_create_embedder', mock_create_embedder)
        
        engine = VectorSearchEngine(mock_config)
        await engine.initialize()
        
        # Search by function name
        results = await engine.search_by_function_name("fibonacci")
        
        # Should return a list
        assert isinstance(results, list)
    
    @pytest.mark.asyncio
    async def test_engine_search_by_class_name(self, mock_config, monkeypatch):
        """Test class name search."""
        # Mock the embedder creation
        def mock_create_embedder(self):
            return MockEmbedder()
        
        monkeypatch.setattr(VectorSearchEngine, '_create_embedder', mock_create_embedder)
        
        engine = VectorSearchEngine(mock_config)
        await engine.initialize()
        
        # Search by class name
        results = await engine.search_by_class_name("Calculator")
        
        # Should return a list
        assert isinstance(results, list)
    
    @pytest.mark.asyncio
    async def test_engine_clear_index(self, mock_config, monkeypatch):
        """Test clearing the index."""
        # Mock the embedder creation
        def mock_create_embedder(self):
            return MockEmbedder()
        
        monkeypatch.setattr(VectorSearchEngine, '_create_embedder', mock_create_embedder)
        
        engine = VectorSearchEngine(mock_config)
        await engine.initialize()
        
        # Clear index (should not raise exception)
        await engine.clear_index()
    
    @pytest.mark.asyncio
    async def test_engine_get_stats(self, mock_config, monkeypatch):
        """Test getting collection statistics."""
        # Mock the embedder creation
        def mock_create_embedder(self):
            return MockEmbedder()
        
        monkeypatch.setattr(VectorSearchEngine, '_create_embedder', mock_create_embedder)
        
        engine = VectorSearchEngine(mock_config)
        await engine.initialize()
        
        # Get stats
        stats = await engine.get_stats()
        
        # Should return a dictionary
        assert isinstance(stats, dict)
    
    @pytest.mark.asyncio
    async def test_engine_close(self, mock_config, monkeypatch):
        """Test closing the engine."""
        # Mock the embedder creation
        def mock_create_embedder(self):
            return MockEmbedder()
        
        monkeypatch.setattr(VectorSearchEngine, '_create_embedder', mock_create_embedder)
        
        engine = VectorSearchEngine(mock_config)
        await engine.initialize()
        
        # Close engine (should not raise exception)
        await engine.close()
        
        # Should not be initialized anymore
        assert not engine._initialized
    
    @pytest.mark.asyncio
    async def test_engine_invalid_embedder_config(self, temp_dir):
        """Test engine with invalid embedder configuration."""
        config = Config(
            embedder_provider=EmbedderProvider.OPENAI,
            # Missing API key
            model_id="text-embedding-3-small",
            model_dimension=1536,
            lancedb_path=str(temp_dir) + "/test_db"
        )
        
        engine = VectorSearchEngine(config)
        
        # Should raise exception during initialization
        with pytest.raises(ValueError):
            await engine.initialize()
    
    @pytest.mark.asyncio
    async def test_engine_auto_initialization(self, mock_config, monkeypatch):
        """Test that engine auto-initializes when needed."""
        # Mock the embedder creation
        def mock_create_embedder(self):
            return MockEmbedder()
        
        monkeypatch.setattr(VectorSearchEngine, '_create_embedder', mock_create_embedder)
        
        engine = VectorSearchEngine(mock_config)
        
        # Should not be initialized
        assert not engine._initialized
        
        # Calling search should auto-initialize
        results = await engine.search("test query")
        
        # Should be initialized now
        assert engine._initialized
        assert isinstance(results, list)

"""Tests for configuration management."""

import pytest
import tempfile
import json
from pathlib import Path

from vector_search.config import Config
from vector_search.models import EmbedderProvider


class TestConfig:
    """Test configuration management."""
    
    def test_default_config(self):
        """Test default configuration values."""
        config = Config()
        
        assert config.embedder_provider == EmbedderProvider.OPENAI
        assert config.lancedb_path == "./vector_db"
        assert config.table_name == "code_vectors"
        assert config.search_min_score == 0.4
        assert config.search_max_results == 50
        assert config.cache_enabled is True
    
    def test_config_validation_openai(self):
        """Test OpenAI configuration validation."""
        config = Config(
            embedder_provider=EmbedderProvider.OPENAI,
            openai_api_key="test-key",
            model_id="text-embedding-3-small",
            model_dimension=1536
        )
        
        # Should not raise an exception
        config.validate()
    
    def test_config_validation_openai_missing_key(self):
        """Test OpenAI configuration validation with missing key."""
        config = Config(
            embedder_provider=EmbedderProvider.OPENAI,
            model_id="text-embedding-3-small",
            model_dimension=1536
        )
        
        with pytest.raises(ValueError, match="OpenAI API key is required"):
            config.validate()
    
    def test_config_validation_ollama(self):
        """Test Ollama configuration validation."""
        config = Config(
            embedder_provider=EmbedderProvider.OLLAMA,
            ollama_base_url="http://localhost:11434",
            model_id="nomic-embed-text",
            model_dimension=768
        )
        
        # Should not raise an exception
        config.validate()
    
    def test_config_validation_gemini(self):
        """Test Gemini configuration validation."""
        config = Config(
            embedder_provider=EmbedderProvider.GEMINI,
            gemini_api_key="test-key",
            model_id="text-embedding-004",
            model_dimension=768
        )
        
        # Should not raise an exception
        config.validate()
    
    def test_config_validation_missing_model_id(self):
        """Test configuration validation with missing model ID."""
        config = Config(
            embedder_provider=EmbedderProvider.OPENAI,
            openai_api_key="test-key",
            model_dimension=1536
        )
        
        with pytest.raises(ValueError, match="Model ID is required"):
            config.validate()
    
    def test_config_validation_invalid_score(self):
        """Test configuration validation with invalid score."""
        config = Config(
            embedder_provider=EmbedderProvider.OPENAI,
            openai_api_key="test-key",
            model_id="text-embedding-3-small",
            model_dimension=1536,
            search_min_score=1.5  # Invalid score > 1
        )
        
        with pytest.raises(ValueError, match="Search min score must be between 0 and 1"):
            config.validate()
    
    def test_config_from_dict(self):
        """Test creating config from dictionary."""
        config_dict = {
            "embedder_provider": "openai",
            "openai_api_key": "test-key",
            "model_id": "text-embedding-3-small",
            "model_dimension": 1536,
            "search_min_score": 0.3
        }
        
        config = Config.from_dict(config_dict)
        
        assert config.embedder_provider == EmbedderProvider.OPENAI
        assert config.openai_api_key == "test-key"
        assert config.model_id == "text-embedding-3-small"
        assert config.model_dimension == 1536
        assert config.search_min_score == 0.3
    
    def test_config_to_dict(self):
        """Test converting config to dictionary."""
        config = Config(
            embedder_provider=EmbedderProvider.OPENAI,
            openai_api_key="test-key",
            model_id="text-embedding-3-small",
            model_dimension=1536
        )
        
        config_dict = config.to_dict()
        
        assert config_dict["embedder_provider"] == EmbedderProvider.OPENAI
        assert config_dict["openai_api_key"] == "test-key"
        assert config_dict["model_id"] == "text-embedding-3-small"
        assert config_dict["model_dimension"] == 1536
    
    def test_config_save_and_load_json(self, temp_dir):
        """Test saving and loading config from JSON file."""
        config = Config(
            embedder_provider=EmbedderProvider.OPENAI,
            openai_api_key="test-key",
            model_id="text-embedding-3-small",
            model_dimension=1536
        )
        
        config_path = Path(temp_dir) / "config.json"
        config.save_to_file(str(config_path))
        
        # Load config (note: sensitive data is masked in saved file)
        loaded_config = Config.from_file(str(config_path))
        
        assert loaded_config.embedder_provider == EmbedderProvider.OPENAI
        assert loaded_config.model_id == "text-embedding-3-small"
        assert loaded_config.model_dimension == 1536
        # API key should be masked
        assert loaded_config.openai_api_key == "***"
    
    def test_config_environment_variables(self, monkeypatch):
        """Test loading configuration from environment variables."""
        monkeypatch.setenv("OPENAI_API_KEY", "env-test-key")
        monkeypatch.setenv("GEMINI_API_KEY", "env-gemini-key")
        
        config = Config(embedder_provider=EmbedderProvider.OPENAI)
        
        assert config.openai_api_key == "env-test-key"
        assert config.gemini_api_key == "env-gemini-key"

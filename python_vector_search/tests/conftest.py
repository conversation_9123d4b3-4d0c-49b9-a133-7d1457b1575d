"""Pytest configuration and fixtures."""

import pytest
import tempfile
import shutil
from pathlib import Path

from vector_search.config import Config
from vector_search.models import EmbedderProvider


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


@pytest.fixture
def sample_config(temp_dir):
    """Create a sample configuration for testing."""
    return Config(
        embedder_provider=EmbedderProvider.OPENAI,
        openai_api_key="test-key",
        model_id="text-embedding-3-small",
        model_dimension=1536,
        lancedb_path=str(Path(temp_dir) / "test_db"),
        cache_path=str(Path(temp_dir) / "cache.json"),
        search_min_score=0.3,
        search_max_results=10
    )


@pytest.fixture
def sample_python_code():
    """Sample Python code for testing."""
    return '''
def fibonacci(n):
    """Calculate the nth Fibonacci number."""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

class Calculator:
    """A simple calculator class."""
    
    def add(self, a, b):
        """Add two numbers."""
        return a + b
    
    def multiply(self, a, b):
        """Multiply two numbers."""
        return a * b
'''


@pytest.fixture
def sample_javascript_code():
    """Sample JavaScript code for testing."""
    return '''
function factorial(n) {
    // Calculate factorial of n
    if (n <= 1) {
        return 1;
    }
    return n * factorial(n - 1);
}

class MathUtils {
    static isPrime(num) {
        if (num <= 1) return false;
        for (let i = 2; i <= Math.sqrt(num); i++) {
            if (num % i === 0) return false;
        }
        return true;
    }
}
'''


@pytest.fixture
def sample_markdown_content():
    """Sample Markdown content for testing."""
    return '''
# Vector Search Documentation

This is a comprehensive guide to using the vector search package.

## Installation

To install the package, run:

```bash
pip install vector-search
```

## Quick Start

Here's a simple example:

```python
from vector_search import VectorSearchEngine
from vector_search.config import Config

config = Config(embedder_provider="openai")
engine = VectorSearchEngine(config)
```

## Features

- Multi-language code parsing
- Semantic search capabilities
- Efficient vector storage
'''


@pytest.fixture
def create_test_files(temp_dir, sample_python_code, sample_javascript_code, sample_markdown_content):
    """Create test files in temporary directory."""
    test_dir = Path(temp_dir) / "test_project"
    test_dir.mkdir()
    
    # Create Python file
    python_file = test_dir / "calculator.py"
    python_file.write_text(sample_python_code)
    
    # Create JavaScript file
    js_file = test_dir / "math_utils.js"
    js_file.write_text(sample_javascript_code)
    
    # Create Markdown file
    md_file = test_dir / "README.md"
    md_file.write_text(sample_markdown_content)
    
    # Create subdirectory with more files
    subdir = test_dir / "utils"
    subdir.mkdir()
    
    utils_py = subdir / "helpers.py"
    utils_py.write_text('''
def helper_function():
    """A helper function."""
    return "helper"

def another_helper():
    """Another helper function."""
    return "another"
''')
    
    return test_dir
